# Zen Icons

A collection of minimal, geometric, and calming icon alternatives designed to replace traditional literal icons with more abstract, zen-inspired representations.

## Philosophy

Traditional icons can be:
- Too literal and anatomical (👁️ eye)
- Old-fashioned and detailed (✏️ pencil)
- Attention-grabbing in a distracting way

Zen icons are:
- **Simple**: Minimal geometric shapes
- **Abstract**: Suggest function without being literal
- **Calming**: Reduce visual noise and cognitive load
- **Consistent**: Follow the same design principles

## Icon Alternatives

### View Actions (replacing 👁️)

| Icon | Concept | Usage |
|------|---------|-------|
| `view()` | Simple circle | Gentle focus/attention without being literal |
| `viewLines()` | Two gentle lines | Abstract representation of reading/viewing content |
| `viewArrow()` | Minimal arrow | Suggests "reveal" or "open" - very subtle |

### Edit Actions (replacing ✏️)

| Icon | Concept | Usage |
|------|---------|-------|
| `edit()` | Simple corner | Abstract geometric shape suggesting modification |
| `editPlus()` | Minimal plus | Universal symbol for "add/modify" - very clean |
| `editCurve()` | Gentle curve | Suggests change/modification in a flowing way |

## Usage

```tsx
import { ZenIcons } from '@/components/ZenIcons'

// Basic usage
<button>
  {ZenIcons.view()}
</button>

// With custom props
<button>
  {ZenIcons.edit({ size: 20, color: '#3b82f6', strokeWidth: 2 })}
</button>

// Individual imports
import { ViewIcon, EditIcon } from '@/components/ZenIcons'

<button>
  <ViewIcon size={16} />
</button>
```

## Props

All icons accept the following optional props:

- `size?: number` - Icon size in pixels (default: 16)
- `color?: string` - Icon color (default: 'currentColor')
- `strokeWidth?: number` - Stroke width (default: 1.5)
- `className?: string` - CSS class name

## Current Implementation

The inbox page currently uses:
- **View**: Simple circle (`ZenIcons.view()`)
- **Edit**: Simple corner (`ZenIcons.edit()`)
- **Delete**: Clean trash icon (`ZenIcons.delete()`)

## Demo

Visit `/zen-icons-demo` to see all icon alternatives in action and compare them side by side.

## Design Principles

1. **Geometric Simplicity**: Use basic shapes (circles, lines, corners)
2. **Minimal Stroke Weight**: Default 1.5px for lightness
3. **Consistent Sizing**: 16px default with scalable options
4. **Abstract Representation**: Suggest function without literal depiction
5. **Emotional Calm**: Reduce visual stress and cognitive load

## Benefits

- **Reduced Visual Noise**: Less distracting than emoji or detailed icons
- **Better Accessibility**: Clearer focus states and hover interactions
- **Consistent Branding**: Aligns with zen UI theme
- **Scalable**: Works at different sizes and contexts
- **Future-Proof**: Abstract concepts age better than literal representations
