'use client'

import React, { useState } from 'react'

interface CopilotNudge {
  id: string
  message: string
  action?: {
    text: string
    onClick: () => void
  }
  type: 'welcome' | 'guidance' | 'tip' | 'reminder'
  dismissible?: boolean
}

interface AICopilotNudgesProps {
  className?: string
  nudges: CopilotNudge[]
  onDismiss?: (nudgeId: string) => void
}

const zenTheme = {
  surface: '#FFFFFF',
  border: '#E5E7EB',
  primaryText: '#0B0F14',
  secondaryText: '#475569',
  accent: '#111827',
  accentBlue: '#2563EB',
  success: '#059669',
  warning: '#F59E0B',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

export default function AICopilotNudges({
  className = '',
  nudges,
  onDismiss,
}: AICopilotNudgesProps) {
  const [dismissedNudges, setDismissedNudges] = useState<Set<string>>(new Set())

  const handleDismiss = (nudgeId: string) => {
    setDismissedNudges(prev => {
      const newSet = new Set(prev)
      newSet.add(nudgeId)
      return newSet
    })
    onDismiss?.(nudgeId)
  }

  const visibleNudges = nudges.filter(nudge => !dismissedNudges.has(nudge.id))

  if (visibleNudges.length === 0) {
    return null
  }

  const getContainerStyle = () => ({
    position: 'fixed' as const,
    bottom: '24px',
    right: '24px',
    width: '320px',
    maxHeight: '400px',
    overflowY: 'auto' as const,
    zIndex: 1000,
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '12px',
  })

  const getNudgeStyle = (type: CopilotNudge['type']) => {
    const baseStyle = {
      background: zenTheme.surface,
      border: `1px solid ${zenTheme.border}`,
      borderRadius: '12px',
      padding: '16px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      position: 'relative' as const,
      animation: 'slideInRight 0.3s ease-out',
    }

    const borderColors = {
      welcome: zenTheme.accentBlue,
      guidance: zenTheme.success,
      tip: zenTheme.warning,
      reminder: zenTheme.accent,
    }

    return {
      ...baseStyle,
      borderLeftColor: borderColors[type],
      borderLeftWidth: '4px',
    }
  }

  const getHeaderStyle = () => ({
    display: 'flex',
    alignItems: 'flex-start',
    gap: '12px',
    marginBottom: '8px',
  })

  const getAvatarStyle = () => ({
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    backgroundColor: zenTheme.accentBlue,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '16px',
    flexShrink: 0,
  })

  const getMessageStyle = () => ({
    fontSize: '14px',
    color: zenTheme.primaryText,
    lineHeight: 1.4,
    margin: 0,
    flex: 1,
  })

  const getDismissButtonStyle = () => ({
    position: 'absolute' as const,
    top: '8px',
    right: '8px',
    background: 'none',
    border: 'none',
    color: zenTheme.secondaryText,
    cursor: 'pointer',
    fontSize: '16px',
    width: '24px',
    height: '24px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'background-color 0.2s ease',
  })

  const getActionButtonStyle = () => ({
    marginTop: '12px',
    padding: '8px 16px',
    backgroundColor: zenTheme.accentBlue,
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    fontSize: '12px',
    fontWeight: 500,
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
  })

  const getTypeIcon = (type: CopilotNudge['type']) => {
    const icons = {
      welcome: '👋',
      guidance: '💡',
      tip: '✨',
      reminder: '⏰',
    }
    return icons[type]
  }

  return (
    <>
      <style jsx>{`
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
      <div className={className} style={getContainerStyle()}>
        {visibleNudges.map((nudge) => (
          <div key={nudge.id} style={getNudgeStyle(nudge.type)}>
            {nudge.dismissible && (
              <button
                style={getDismissButtonStyle()}
                onClick={() => handleDismiss(nudge.id)}
                aria-label="Dismiss"
              >
                ×
              </button>
            )}
            
            <div style={getHeaderStyle()}>
              <div style={getAvatarStyle()}>
                {getTypeIcon(nudge.type)}
              </div>
              <p style={getMessageStyle()}>{nudge.message}</p>
            </div>

            {nudge.action && (
              <button
                style={getActionButtonStyle()}
                onClick={nudge.action.onClick}
              >
                {nudge.action.text}
              </button>
            )}
          </div>
        ))}
      </div>
    </>
  )
}

// Predefined nudge configurations for common scenarios
export const createWelcomeNudges = (actions: {
  onUploadDocument: () => void
  onSetupVAT: () => void
  onConnectBank: () => void
}) => [
  {
    id: 'welcome',
    type: 'welcome' as const,
    message: "👋 Welcome to BelBooks! Let's get you started by uploading your first supplier invoice.",
    action: {
      text: 'Upload Document',
      onClick: actions.onUploadDocument,
    },
    dismissible: true,
  },
  {
    id: 'vat-setup',
    type: 'guidance' as const,
    message: "💡 Set up VAT to track your tax position in real-time and stay compliant with Belgian requirements.",
    action: {
      text: 'Configure VAT',
      onClick: actions.onSetupVAT,
    },
    dismissible: true,
  },
  {
    id: 'bank-connection',
    type: 'tip' as const,
    message: "✨ Connect your bank account to enable automatic transaction matching and reconciliation.",
    action: {
      text: 'Connect Bank',
      onClick: actions.onConnectBank,
    },
    dismissible: true,
  },
]
