'use client'

import { useState } from 'react'
import { useOrgEntity } from '@/contexts/OrgEntityContext'
import type { Database } from '@ledgerly/types'

type UserTenant = Database['public']['Views']['v_user_tenants']['Row']
type UserEntity = Database['public']['Views']['v_user_entities']['Row']

const ChevronDownIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
)

export function OrgEntityPicker() {
  const {
    selection,
    tenants,
    entities,
    loading,
    error,
    selectTenant,
    selectEntity,
  } = useOrgEntity()

  const [isOpen, setIsOpen] = useState(false)

  if (loading) {
    return (
      <div className="w-48 h-10 bg-gray-100 animate-pulse rounded-md"></div>
    )
  }

  if (error) {
    return (
      <div className="w-48 p-2 text-sm text-red-600 bg-red-50 rounded-md">
        Error loading organizations
      </div>
    )
  }

  if (tenants.length === 0) {
    return (
      <div className="w-48 p-2 text-sm text-gray-500 bg-gray-50 rounded-md">
        No organizations found
      </div>
    )
  }

  const currentDisplayName = () => {
    if (selection.mode === 'entity' && selection.entityName) {
      return `${selection.entityName} (${selection.tenantName})`
    }
    return selection.tenantName || 'Select Organization'
  }

  const handleTenantSelect = (tenant: UserTenant | null) => {
    selectTenant(tenant)
    setIsOpen(false)
  }

  const handleEntitySelect = (entity: UserEntity | null) => {
    selectEntity(entity)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-48 px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <span className="truncate">{currentDisplayName()}</span>
        <ChevronDownIcon
          className={`w-4 h-4 text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute z-20 w-64 mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
            <div className="max-h-60 overflow-y-auto">
              {/* Tenants Section */}
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Organizations
              </div>

              {tenants.map(tenant => (
                <div key={tenant.tenant_id}>
                  <button
                    onClick={() => handleTenantSelect(tenant)}
                    className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 flex items-center justify-between ${
                      selection.tenantId === tenant.tenant_id &&
                      selection.mode === 'tenant'
                        ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500'
                        : 'text-gray-900'
                    }`}
                  >
                    <div>
                      <div className="font-medium">{tenant.tenant_name}</div>
                      <div className="text-xs text-gray-500 capitalize">
                        {tenant.tenant_kind?.toLowerCase()} •{' '}
                        {tenant.role?.replace('tenant_', '') || 'member'}
                      </div>
                    </div>
                    {selection.tenantId === tenant.tenant_id &&
                      selection.mode === 'tenant' && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                  </button>

                  {/* Entities under this tenant */}
                  {selection.tenantId === tenant.tenant_id && (
                    <div className="bg-gray-50">
                      {entities
                        .filter(e => e.tenant_id === tenant.tenant_id)
                        .map(entity => (
                          <button
                            key={entity.entity_id}
                            onClick={() => handleEntitySelect(entity)}
                            className={`w-full px-6 py-2 text-sm text-left hover:bg-gray-100 flex items-center justify-between ${
                              selection.entityId === entity.entity_id &&
                              selection.mode === 'entity'
                                ? 'bg-blue-50 text-blue-900 border-r-2 border-blue-500'
                                : 'text-gray-700'
                            }`}
                          >
                            <div>
                              <div className="font-medium">
                                {entity.entity_name}
                              </div>
                              <div className="text-xs text-gray-500">
                                Entity • {entity.role || 'member'}
                              </div>
                            </div>
                            {selection.entityId === entity.entity_id &&
                              selection.mode === 'entity' && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                          </button>
                        ))}

                      {entities.filter(e => e.tenant_id === tenant.tenant_id)
                        .length === 0 && (
                        <div className="px-6 py-2 text-xs text-gray-500 italic">
                          No entities in this organization
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="border-t border-gray-100 px-3 py-2">
              <button
                onClick={() => {
                  // TODO: Implement create organization flow
                  setIsOpen(false)
                }}
                className="w-full px-2 py-1 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded text-left"
              >
                + Create Organization
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
