# Ledgerly Web Application

A modern Next.js 14 web application for accounting and ledger management.

## Features

- **Dashboard**: Environment connectivity status and overview
- **Inbox**: Document management and processing queue
- **Ledger**: Trial balance and account management
- **VAT**: VAT returns, rates, and compliance management

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase
- **Package Manager**: pnpm (workspace)

## Development

### Prerequisites

- Node.js 18+
- pnpm 9+
- Supabase account and project

### Environment Setup

1. Copy the environment template:
   ```bash
   cp .env.local.example .env.local
   ```

2. Fill in your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

### Running the Application

```bash
# Install dependencies (from workspace root)
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Run linting
pnpm lint

# Run type checking
pnpm typecheck
```

## Project Structure

```
apps/web/
├── app/                 # Next.js App Router
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Dashboard page
│   ├── inbox/          # Inbox feature
│   ├── ledger/         # Ledger feature
│   └── vat/            # VAT feature
├── components/         # React components
│   └── Navigation.tsx  # Navigation sidebar
├── lib/                # Utilities and configurations
│   └── supabase.ts     # Supabase client
└── ...                 # Config files
```

## Dependencies

### Workspace Dependencies
- `@ledgerly/types` - Shared TypeScript types
- `@ledgerly/dal` - Data access layer

### External Dependencies
- `@supabase/supabase-js` - Supabase client
- `next` - Next.js framework
- `react` - React library
- `tailwindcss` - CSS framework