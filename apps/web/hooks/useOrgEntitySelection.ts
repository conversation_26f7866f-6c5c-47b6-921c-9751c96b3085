'use client'

import { useOrgEntity } from '@/contexts/OrgEntityContext'

/**
 * Convenience hook for org/entity selection state
 */
export function useOrgEntitySelection() {
  const { selection, tenants, entities, loading, error } = useOrgEntity()
  
  const currentTenant = tenants.find(t => t.tenant_id === selection.tenantId)
  const currentEntity = entities.find(e => e.entity_id === selection.entityId)
  
  const availableEntitiesForTenant = entities.filter(e => e.tenant_id === selection.tenantId)
  
  const isValid = !!(selection.tenantId && (
    selection.mode === 'tenant' || 
    (selection.mode === 'entity' && selection.entityId)
  ))
  
  return {
    // Current selection state
    selection,
    currentTenant,
    currentEntity,
    
    // Selection validity
    isValid,
    
    // Available options
    tenants,
    entities,
    availableEntitiesForTenant,
    
    // Loading/error state
    loading,
    error,
    
    // Helper getters
    hasMultipleTenants: tenants.length > 1,
    hasEntitiesInCurrentTenant: availableEntitiesForTenant.length > 0,
    
    // Display helpers
    getDisplayName: () => {
      if (selection.mode === 'entity' && currentEntity) {
        return `${currentEntity.entity_name} (${currentTenant?.tenant_name})`
      }
      return currentTenant?.tenant_name || 'No Organization'
    }
  }
}