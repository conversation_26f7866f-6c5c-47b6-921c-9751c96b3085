{"name": "@ledgerly/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:single": "vitest run --reporter=verbose", "test:changed": "vitest --changed", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "vitest run --config vitest.integration.config.ts", "test:e2e": "playwright test"}, "dependencies": {"@ledgerly/dal": "workspace:*", "@ledgerly/types": "workspace:*", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.85.6", "next": "14.2.15", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.13", "zxcvbn": "^4.4.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/styled-components": "^5.1.34", "@types/zxcvbn": "^4.4.5", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "vitest": "^3.2.4"}}