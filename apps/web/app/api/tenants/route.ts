import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type {
  TenantResponse,
  TenantsListResponse,
  ErrorResponse,
} from '@/lib/api-types'
import { isTenantRequest } from '@/lib/api-types'
import { createSecureServerClient } from '@/lib/supabase-server'

/**
 * Create a new tenant
 * This route requires CSRF protection as it performs state-changing operations
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Parse and validate request body
    const body: unknown = await request.json()

    if (!isTenantRequest(body)) {
      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid request body. Required field: name' },
        400
      )
    }

    const { name, description } = body

    // Create tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert([
        {
          name: name.trim(),
          description: description?.trim() || null,
          created_by: user.id,
        },
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating tenant:', error)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to create tenant' },
        500
      )
    }

    // Add user as admin of the new tenant
    const { error: membershipError } = await supabase
      .from('tenant_memberships')
      .insert([
        {
          tenant_id: tenant.id,
          user_id: user.id,
          role: 'admin',
        },
      ])

    if (membershipError) {
      console.error('Error creating tenant membership:', membershipError)
      // Don't fail the request, but log the error
    }

    return createJsonResponse<TenantResponse>({ tenant })
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}

/**
 * Get user's tenants
 * This is a safe method and doesn't require CSRF protection
 */
export async function GET(_request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    const { data: tenants, error } = await supabase
      .from('v_user_tenants')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tenants:', error)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to fetch tenants' },
        500
      )
    }

    return createJsonResponse<TenantsListResponse>({ tenants })
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}
