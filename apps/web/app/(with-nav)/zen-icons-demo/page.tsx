'use client'

import React, { CSSProperties } from 'react'
import { ZenIcons } from '@/components/ZenIcons'

// Zen UI Theme
const zenTheme = {
  bg: '#FBFAF5',
  surface: '#FFFFFC',
  primaryText: '#1a1a1a',
  secondaryText: '#6b7280',
  subtleText: '#9ca3af',
  border: '#f3f4f6',
  borderHover: '#e5e7eb',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  primaryAction: '#3b82f6',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
}

const getCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px',
  boxShadow: theme.shadow,
  padding: '24px',
  marginBottom: '24px',
})

const getIconButtonStyle = (theme: typeof zenTheme): CSSProperties => ({
  padding: '12px',
  background: 'none',
  border: `1px solid ${theme.border}`,
  color: theme.secondaryText,
  cursor: 'pointer',
  borderRadius: '6px',
  transition: 'all 0.15s ease',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '8px',
})

export default function ZenIconsDemo() {
  return (
    <div style={{ 
      background: zenTheme.bg, 
      minHeight: '100vh', 
      padding: '24px',
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif"
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: 600, 
          color: zenTheme.primaryText, 
          marginBottom: '8px' 
        }}>
          Zen Icons Demo
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: zenTheme.secondaryText, 
          marginBottom: '32px' 
        }}>
          Minimal, geometric, calming alternatives to traditional icons
        </p>

        {/* View Icon Options */}
        <div style={getCardStyle(zenTheme)}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: 600, 
            color: zenTheme.primaryText, 
            marginBottom: '16px' 
          }}>
            View Action Icons (replacing 👁️)
          </h2>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.view()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Simple Circle<br/>
                <small>Gentle focus/attention</small>
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.viewLines()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Two Gentle Lines<br/>
                <small>Reading/viewing content</small>
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.viewArrow()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Minimal Arrow<br/>
                <small>Reveal/open</small>
              </p>
            </div>
          </div>
        </div>

        {/* Edit Icon Options */}
        <div style={getCardStyle(zenTheme)}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: 600, 
            color: zenTheme.primaryText, 
            marginBottom: '16px' 
          }}>
            Edit Action Icons (replacing ✏️)
          </h2>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.edit()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Simple Corner<br/>
                <small>Abstract modification</small>
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.editPlus()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Minimal Plus<br/>
                <small>Universal add/modify</small>
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <button style={getIconButtonStyle(zenTheme)}>
                {ZenIcons.editCurve()}
              </button>
              <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                Gentle Curve<br/>
                <small>Flowing change</small>
              </p>
            </div>
          </div>
        </div>

        {/* Current Implementation */}
        <div style={getCardStyle(zenTheme)}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: 600, 
            color: zenTheme.primaryText, 
            marginBottom: '16px' 
          }}>
            Current Implementation (Inbox Page)
          </h2>
          <div style={{ display: 'flex', gap: '4px' }}>
            <button
              style={{
                padding: '8px',
                background: 'none',
                border: 'none',
                color: zenTheme.secondaryText,
                cursor: 'pointer',
                borderRadius: '6px',
                transition: 'all 0.15s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = zenTheme.primaryText;
                e.currentTarget.style.backgroundColor = zenTheme.border;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = zenTheme.secondaryText;
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {ZenIcons.view()}
            </button>
            <button
              style={{
                padding: '8px',
                background: 'none',
                border: 'none',
                color: zenTheme.secondaryText,
                cursor: 'pointer',
                borderRadius: '6px',
                transition: 'all 0.15s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = zenTheme.primaryText;
                e.currentTarget.style.backgroundColor = zenTheme.border;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = zenTheme.secondaryText;
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {ZenIcons.edit()}
            </button>
            <button
              style={{
                padding: '8px',
                background: 'none',
                border: 'none',
                color: zenTheme.secondaryText,
                cursor: 'pointer',
                borderRadius: '6px',
                transition: 'all 0.15s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = zenTheme.error;
                e.currentTarget.style.backgroundColor = zenTheme.border;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = zenTheme.secondaryText;
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {ZenIcons.delete()}
            </button>
          </div>
          <p style={{ fontSize: '14px', color: zenTheme.subtleText, marginTop: '16px' }}>
            Currently using: Simple Circle (view) + Simple Corner (edit) + Clean Delete
          </p>
        </div>

        {/* Additional Icons */}
        <div style={getCardStyle(zenTheme)}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: 600, 
            color: zenTheme.primaryText, 
            marginBottom: '16px' 
          }}>
            Additional Zen Icons
          </h2>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            {[
              { icon: ZenIcons.upload(), name: 'Upload' },
              { icon: ZenIcons.download(), name: 'Download' },
              { icon: ZenIcons.search(), name: 'Search' },
              { icon: ZenIcons.settings(), name: 'Settings' },
              { icon: ZenIcons.close(), name: 'Close' },
              { icon: ZenIcons.delete(), name: 'Delete' },
            ].map(({ icon, name }) => (
              <div key={name} style={{ textAlign: 'center' }}>
                <button style={getIconButtonStyle(zenTheme)}>
                  {icon}
                </button>
                <p style={{ fontSize: '14px', color: zenTheme.subtleText, margin: '8px 0' }}>
                  {name}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
