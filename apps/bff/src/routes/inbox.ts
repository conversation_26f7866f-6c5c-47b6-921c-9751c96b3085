/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { FastifyInstance } from 'fastify'
import { SupabaseClient } from '@supabase/supabase-js'
import {
  DocumentUploadRequest,
  ConfirmRequest,
  FeatureFlagRequest,
  ApiResponse,
  Database,
  ExtractionResult,
  Suggestion,
  SuggestionLine
} from '@ledgerly/types'
import { z } from 'zod'
import type { ExtendedSupabaseClient } from '../types'
import '../types' // Import type extensions


// Typed query results
interface FeatureFlagResult {
  enabled: boolean
  config: Record<string, unknown>
}

interface CodingEventResult {
  id: number
}

// Error handling interfaces
interface ErrorWithMessage {
  message: string
  [key: string]: unknown
}

function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return typeof error === 'object' && error !== null && 'message' in error && typeof (error as Record<string, unknown>).message === 'string'
}

// Query parameter schemas
const EntityParamsSchema = z.object({
  entityId: z.string().transform(val => parseInt(val, 10))
})

const DocumentParamsSchema = z.object({
  documentId: z.string().transform(val => parseInt(val, 10))
})

const DocumentListQuerySchema = z.object({
  status: z.string().optional(),
  page: z.string().transform(val => parseInt(val, 10)).optional().default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).optional().default('20')
})

export default async function inboxRoutes(fastify: FastifyInstance) {
  // Helper function to verify feature flag
  const checkFeatureFlag = async (supabaseClient: SupabaseClient<Database>, entityId: number, flag: string): Promise<FeatureFlagResult> => {
    const { data } = await supabaseClient
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', flag)
      .single()

    return {
      enabled: data?.enabled || false,
      config: (data?.config as Record<string, unknown>) || {}
    }
  }

  // Upload document endpoint
  fastify.post('/entities/:entityId/documents', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const uploadRequest = DocumentUploadRequest.parse(request.body)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check feature flag
      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        const response: ApiResponse = {
          success: false,
          error: 'Inbox feature not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Insert document record (file_hash will be computed by worker from actual file content)
      const { data: document, error } = await supabaseClient
        .from('inbox_documents')
        .insert({
          entity_id: entityId,
          path: uploadRequest.path,
          file_hash: 'placeholder', // Will be updated by worker
          mime_type: uploadRequest.mime_type,
          source: uploadRequest.source,
          status: 'uploaded'
        })
        .select('id')
        .single()

      if (error) {
        fastify.log.error(`Failed to insert document: ${error.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to create document record'
        }
        return reply.code(500).send(response)
      }

      // Generate signed URL for workers
      const { data: signedUrl } = await fastify.supabase.storage
        .from('inbox')
        .createSignedUrl(uploadRequest.path, 3600)

      if (!signedUrl) {
        const response: ApiResponse = {
          success: false,
          error: 'Failed to generate file URL for processing'
        }
        return reply.code(500).send(response)
      }

      // Call workers to process document
      try {
        const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'
        const workersResponse = await fetch(`${workersUrl}/process-inbox-document`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            document_id: document.id,
            entity_id: entityId,
            file_url: signedUrl.signedUrl
          })
        })

        if (!workersResponse.ok) {
          throw new Error(`Workers responded with status ${workersResponse.status}`)
        }

        const workersData = await workersResponse.json() as { task_id: string }
        fastify.log.info(`Document processing started with task ${workersData.task_id}`)

      } catch (workersError) {
        fastify.log.error(`Failed to start processing: ${workersError instanceof Error ? workersError.message : String(workersError)}`)
        // Document is created but processing failed - not a fatal error
      }

      const response: ApiResponse = {
        success: true,
        data: {
          document_id: document.id,
          status: 'uploaded',
          message: 'Document uploaded and processing started'
        }
      }

      return response

    } catch (error: unknown) {
      fastify.log.error(`Upload failed: ${String(error)}`)

      const errorMessage = isErrorWithMessage(error) ? error.message : 'Internal server error'
      const response: ApiResponse = {
        success: false,
        error: errorMessage
      }

      return reply.code(errorMessage.includes('not enabled') ? 403 : 500).send(response)
    }
  })

  // List documents endpoint
  fastify.get('/entities/:entityId/documents', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const { status, page, limit } = DocumentListQuerySchema.parse(request.query)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check feature flag
      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        const response: ApiResponse = {
          success: false,
          error: 'Inbox feature not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Build query
      let query = supabaseClient
        .from('inbox_documents')
        .select(`
          id, entity_id, path, mime_type, source, status, confidence,
          created_at, updated_at, error_msg,
          extraction->supplier->>name as supplier_name,
          extraction->invoice->>number as invoice_number,
          extraction->invoice->>issue_date as invoice_date,
          extraction->invoice->>gross as gross_amount
        `, { count: 'exact' })
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      // Apply pagination
      const offset = (page - 1) * limit
      const { data: documents, error, count } = await query.range(offset, offset + limit - 1)

      if (error) {
        fastify.log.error(`Failed to fetch documents: ${error.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to fetch documents'
        }
        return reply.code(500).send(response)
      }

      const totalPages = count ? Math.ceil(count / limit) : 0

      const response: ApiResponse = {
        success: true,
        data: {
          documents: documents || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            pages: totalPages
          }
        }
      }

      return response

    } catch (error: unknown) {
      fastify.log.error(`List documents failed: ${String(error)}`)

      const errorMessage = isErrorWithMessage(error) ? error.message : 'Internal server error'
      const response: ApiResponse = {
        success: false,
        error: errorMessage
      }

      return reply.code(500).send(response)
    }
  })

  // Get document detail
  fastify.get('/documents/:documentId', async (request, reply) => {
    try {
      const { documentId } = DocumentParamsSchema.parse(request.params)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Fetch document
      const { data: document, error } = await supabaseClient
        .from('inbox_documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        const response: ApiResponse = {
          success: false,
          error: 'Document not found'
        }
        return reply.code(404).send(response)
      }

      const response: ApiResponse = {
        success: true,
        data: document
      }

      return response

    } catch (error: unknown) {
      fastify.log.error(`Get document failed: ${String(error)}`)

      const errorMessage = isErrorWithMessage(error) ? error.message : 'Internal server error'
      const response: ApiResponse = {
        success: false,
        error: errorMessage
      }

      return reply.code(500).send(response)
    }
  })

  // Confirm document - HARDENED VERSION
  fastify.post('/documents/:documentId/confirm', async (request, reply) => {
    const requestId = Math.random().toString(36).substring(2)
    const startTime = Date.now()
    let documentId: number | undefined

    try {
      const params = DocumentParamsSchema.parse(request.params)
      documentId = params.documentId
      const confirmRequest = ConfirmRequest.parse(request.body)

      // CRITICAL: Enforce JWT authentication and RLS - NO FALLBACK
      if (!request.userSupabase) {
        fastify.log.warn(`Confirm attempt without JWT token - requestId: ${requestId}, documentId: ${documentId}`)
        const response: ApiResponse = {
          success: false,
          error: 'Authentication required'
        }
        return reply.code(401).send(response)
      }

      const supabaseClient = request.userSupabase
      const userId = request.user?.id // Extract from JWT payload

      if (!userId) {
        fastify.log.warn(`Confirm attempt with invalid JWT - requestId: ${requestId}, documentId: ${documentId}`)
        const response: ApiResponse = {
          success: false,
          error: 'Invalid authentication token'
        }
        return reply.code(401).send(response)
      }

      // CONCURRENCY PROTECTION: Acquire advisory lock
      const { data: lockAcquired } = await (supabaseClient as ExtendedSupabaseClient)
        .rpc('pg_try_advisory_xact_lock', { key1: 42, key2: documentId })

      if (!lockAcquired) {
        fastify.log.warn(`Document ${documentId} already being processed - requestId: ${requestId}, userId: ${userId}`)
        const response: ApiResponse = {
          success: false,
          error: 'Document is currently being processed by another request'
        }
        return reply.code(409).send(response)
      }

      // Fetch document with RLS enforcement
      const { data: document, error } = await supabaseClient
        .from('inbox_documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        fastify.log.warn(`Document not found or access denied - requestId: ${requestId}, documentId: ${documentId}, userId: ${userId}, error: ${error?.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Document not found or access denied'
        }
        return reply.code(403).send(response)
      }

      // IDEMPOTENCY CHECK: Already processed?
      if (document.posted_journal_id || document.export_ref) {
        fastify.log.info(`Document ${documentId} already processed - requestId: ${requestId}, userId: ${userId}, status: ${document.status}, journalId: ${document.posted_journal_id}, exportRef: ${document.export_ref}`)

        const response: ApiResponse = {
          success: true,
          data: {
            document_id: documentId,
            status: document.status,
            journal_id: document.posted_journal_id,
            export_ref: document.export_ref,
            message: `Document already ${document.status}`,
            idempotent: true
          }
        }
        return reply.code(200).send(response)
      }

      // State validation
      if (!['suggested', 'extracted'].includes(document.status)) {
        const response: ApiResponse = {
          success: false,
          error: `Cannot confirm document in status '${document.status}'. Must be 'extracted' or 'suggested'.`
        }
        return reply.code(400).send(response)
      }

      // Handle corrections if provided
      let workingExtraction: ExtractionResult | null = document.extraction as ExtractionResult | null
      let workingSuggestion: Suggestion | null = document.suggestion as Suggestion | null

      if (confirmRequest.correction) {
        fastify.log.info(`Applying correction to document ${documentId} - requestId: ${requestId}, userId: ${userId}`)
        workingExtraction = confirmRequest.correction

        // Re-generate suggestion from corrected extraction
        const { extractionToSuggestion, getSupplierTemplate } = await import('../mappers/suggestionMapper')

        const supplierTemplate = await getSupplierTemplate(
          supabaseClient,
          document.entity_id,
          workingExtraction?.supplier?.vat,
          workingExtraction?.supplier?.name
        )

        try {
          workingSuggestion = await extractionToSuggestion(
            supabaseClient,
            document.entity_id,
            workingExtraction,
            supplierTemplate || undefined
          )
        } catch (suggestionError: unknown) {
          const errorMessage = isErrorWithMessage(suggestionError) ? suggestionError.message : 'Unknown error'
          const response: ApiResponse = {
            success: false,
            error: `Failed to generate suggestion from correction: ${errorMessage}`
          }
          return reply.code(422).send(response)
        }
      }

      if (!workingSuggestion) {
        const response: ApiResponse = {
          success: false,
          error: 'No suggestion available for confirmation'
        }
        return reply.code(400).send(response)
      }

      // BALANCE VALIDATION: Pre-flight check
      const { validateSuggestionBalance } = await import('../mappers/suggestionMapper')
      const balanceCheck = validateSuggestionBalance(workingSuggestion)

      if (!balanceCheck.balanced) {
        fastify.log.error(`Journal not balanced for document ${documentId} - requestId: ${requestId}, userId: ${userId}, error: ${balanceCheck.error}`)
        const response: ApiResponse = {
          success: false,
          error: `Journal is not balanced: ${balanceCheck.error}`
        }
        return reply.code(422).send(response)
      }

      // Determine entity operating mode
      const { data: operatingMode, error: operatingModeError } = await supabaseClient
        .from('operating_modes')
        .select('mode, config')
        .eq('entity_id', document.entity_id)
        .single()

      if (operatingModeError) {
        fastify.log.error(`Failed to fetch operating mode - requestId: ${requestId}, entityId: ${document.entity_id}, error: ${operatingModeError.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to determine entity operating mode'
        }
        return reply.code(500).send(response)
      }

      const entityMode = operatingMode?.mode || 'assist'
      let finalStatus: string
      let journalId: number | undefined
      let exportRef: string | undefined

      // Process based on entity mode
      if (entityMode === 'ledger') {
        // LEDGER MODE: Post to journal
        try {
          if (!workingSuggestion) {
            throw new Error('No suggestion available for posting')
          }

          const postJournalCall = {
            p_entity: document.entity_id,
            p_type: 'purchase',
            p_description: workingSuggestion.description,
            p_date: workingSuggestion.journalDate,
            p_lines: workingSuggestion.lines.map((line: SuggestionLine) => ({
              account_id: line.accountId,
              debit_amount: parseFloat(line.debit) || null,
              credit_amount: parseFloat(line.credit) || null,
              vat_code_id: line.vatCodeId,
              description: line.memo
            })),
            ...(workingSuggestion.reference && { p_reference: workingSuggestion.reference })
          }

          const { data: journalResult, error: journalError } = await supabaseClient
            .rpc('rpc_post_journal', postJournalCall)

          if (journalError) {
            throw new Error(`Journal posting failed: ${journalError.message}`)
          }

          journalId = journalResult
          finalStatus = 'posted'

          fastify.log.info(`Journal posted successfully - requestId: ${requestId}, documentId: ${documentId}, journalId: ${journalId}, userId: ${userId}`)

        } catch (journalError: unknown) {
          const errorMessage = journalError instanceof Error ? journalError.message : 'Unknown error'
          fastify.log.error(`Journal posting failed for document ${documentId} - requestId: ${requestId}, userId: ${userId}, error: ${errorMessage}`)
          const response: ApiResponse = {
            success: false,
            error: `Failed to post journal entry: ${errorMessage}`
          }
          return reply.code(500).send(response)
        }

      } else {
        // ASSIST MODE: Create export event
        const { extractionToExportPayload, createExportDomainEvent, generateExportRef } = await import('../mappers/exportMapper')

        try {
          if (!workingExtraction) {
            throw new Error('No extraction data available for export')
          }
          const exportPayload = await extractionToExportPayload(
            supabaseClient,
            document.entity_id,
            documentId,
            workingExtraction
          )

          exportRef = generateExportRef(documentId)
          const domainEvent = createExportDomainEvent(document.entity_id, documentId, exportPayload)

          // Insert domain event
          const { error: eventError } = await supabaseClient
            .from('domain_events')
            .insert([domainEvent])

          if (eventError) {
            throw new Error(`Failed to create export event: ${eventError.message}`)
          }

          finalStatus = 'exported'

          fastify.log.info(`Export event created successfully - requestId: ${requestId}, documentId: ${documentId}, exportRef: ${exportRef}, userId: ${userId}`)

        } catch (exportError: unknown) {
          const errorMessage = exportError instanceof Error ? exportError.message : 'Unknown error'
          fastify.log.error(`Export processing failed for document ${documentId} - requestId: ${requestId}, userId: ${userId}, error: ${errorMessage}`)
          const response: ApiResponse = {
            success: false,
            error: `Failed to process export: ${errorMessage}`
          }
          return reply.code(500).send(response)
        }
      }

      // Update document with final state and audit fields
      const updateData = {
        status: finalStatus,
        confirmed_at: new Date().toISOString(),
        confirmed_by: userId,
        updated_at: new Date().toISOString(),
        ...(workingExtraction !== document.extraction && { extraction: workingExtraction }),
        ...(workingSuggestion !== document.suggestion && { suggestion: workingSuggestion }),
        ...(journalId && { posted_journal_id: journalId }),
        ...(exportRef && { export_ref: exportRef })
      }

      const { error: updateError } = await supabaseClient
        .from('inbox_documents')
        .update(updateData)
        .eq('id', documentId)

      if (updateError) {
        fastify.log.error(`Failed to update document after processing - requestId: ${requestId}, documentId: ${documentId}, userId: ${userId}, error: ${updateError.message}`)
        // This is non-fatal but should be logged for monitoring
      }

      // AUDIT LOGGING: Record confirmation event
      try {
        await supabaseClient
          .from('audit_events')
          .insert({
            entity_id: document.entity_id,
            table_name: 'inbox_documents',
            record_id: documentId,
            operation: 'UPDATE',
            old_data: { status: document.status },
            new_data: { status: finalStatus },
            actor_user_id: userId
          })
      } catch (auditError: unknown) {
        const errorMessage = auditError instanceof Error ? auditError.message : 'Unknown error'
        fastify.log.error(`Failed to create audit event - requestId: ${requestId}, documentId: ${documentId}, userId: ${userId}, error: ${errorMessage}`)
        // Non-fatal - continue
      }

      // TRACK G - AI LEARNING: Create coding event for AI training
      try {
        // Check if AI suggestions are enabled for this entity
        const { data: aiSuggestFlag } = await supabaseClient
          .from('feature_flags')
          .select('enabled')
          .eq('entity_id', document.entity_id)
          .eq('flag', 'AISuggestEnabled')
          .single()

        if (aiSuggestFlag?.enabled && workingSuggestion?.lines?.length > 0) {
          // Extract the primary account and VAT code from the suggestion
          const primaryLine = workingSuggestion.lines[0]
          const accountId = primaryLine?.accountId
          const vatCodeId = primaryLine?.vatCodeId

          if (accountId) {
            // Create coding event record
            const codingEventData = {
              entity_id: document.entity_id,
              document_id: documentId,
              supplier_name: workingExtraction?.supplier?.name || null,
              supplier_vat: workingExtraction?.supplier?.vat || null,
              account_id: accountId,
              vat_code_id: vatCodeId || null,
              journal_id: journalId || null
            }

            const { data: codingEvent, error: codingError } = await (supabaseClient as ExtendedSupabaseClient)
              .from('coding_events')
              .insert(codingEventData)
              .select('id')
              .single() as { data: CodingEventResult | null, error: Error | null }

            if (codingError) {
              fastify.log.error(`Failed to create coding event - requestId: ${requestId}, documentId: ${documentId}, error: ${codingError.message}`)
            } else if (codingEvent?.id) {
              // Queue background task to generate embeddings
              try {
                const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'
                const indexTaskResponse = await fetch(`${workersUrl}/queue-index-coding-event`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    coding_event_id: codingEvent.id
                  })
                })

                if (indexTaskResponse.ok) {
                  fastify.log.info(`Queued embedding generation for coding event ${codingEvent.id} - requestId: ${requestId}`)
                } else {
                  fastify.log.warn(`Failed to queue embedding generation for coding event ${codingEvent.id} - requestId: ${requestId}`)
                }
              } catch (queueError) {
                fastify.log.error(`Error queuing embedding task - requestId: ${requestId}, codingEventId: ${codingEvent.id}, error: ${queueError instanceof Error ? queueError.message : String(queueError)}`)
                // Non-fatal - continue
              }
            }
          }
        }
      } catch (codingEventError: unknown) {
        const errorMessage = codingEventError instanceof Error ? codingEventError.message : 'Unknown error'
        fastify.log.error(`Failed to create coding event for AI learning - requestId: ${requestId}, documentId: ${documentId}, error: ${errorMessage}`)
        // Non-fatal - continue with document confirmation
      }

      const latency = Date.now() - startTime
      fastify.log.info(`Document confirmation completed - requestId: ${requestId}, documentId: ${documentId}, userId: ${userId}, entityId: ${document.entity_id}, mode: ${entityMode}, status: ${finalStatus}, latencyMs: ${latency}`)

      const response: ApiResponse = {
        success: true,
        data: {
          document_id: documentId,
          status: finalStatus,
          journal_id: journalId,
          export_ref: exportRef,
          message: `Document ${finalStatus === 'posted' ? 'posted to journal' : 'exported for processing'} successfully`
        }
      }

      return reply.code(200).send(response)

    } catch (error: unknown) {
      const latency = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : 'Internal server error'
      fastify.log.error(`Confirm document failed - requestId: ${requestId}, documentId: ${documentId || 'unknown'}, latencyMs: ${latency}, error: ${errorMessage}`)

      const response: ApiResponse = {
        success: false,
        error: errorMessage
      }

      return reply.code(500).send(response)
    }
  })

  // Feature flag check
  fastify.get('/entities/:entityId/feature-flags', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const featureFlagRequest = FeatureFlagRequest.parse({ entity_id: entityId, flag: (request.query as { flag: string }).flag })

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, featureFlagRequest.flag)

      const response: ApiResponse = {
        success: true,
        data: featureFlag
      }

      return response

    } catch (error: unknown) {
      fastify.log.error(`Feature flag check failed: ${String(error)}`)

      const errorMessage = isErrorWithMessage(error) ? error.message : 'Internal server error'
      const response: ApiResponse = {
        success: false,
        error: errorMessage
      }

      return reply.code(500).send(response)
    }
  })
}
