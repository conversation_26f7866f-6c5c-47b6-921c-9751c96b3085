/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback, FastifyRequest, FastifyReply } from 'fastify'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@ledgerly/types'
import { invoiceToUblInput } from '../mappers/invoiceToUbl'
import { generateUblInvoice } from '@ledgerly/domain-invoicing'

interface JwtPayload {
  sub?: string
  user_id?: string
  [key: string]: unknown
}

interface InvoiceParams {
  invoiceId: string
}

function decodeJwtUserId(authHeader?: string): string | null {
  if (!authHeader) return null
  const token = authHeader.replace(/^Bearer\s+/i, '').trim()
  const parts = token.split('.')
  if (parts.length !== 3 || !parts[1]) return null
  try {
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf8')) as JwtPayload
    return payload.sub ?? payload.user_id ?? null
  } catch {
    return null
  }
}

const handler: FastifyPluginCallback = async (fastify) => {
  const registerHandler = async (request: FastifyRequest<{ Params: InvoiceParams }>, reply: FastifyReply) => {
    const authHeader = request.headers['authorization']
    const userId = decodeJwtUserId(authHeader)
    if (!userId) {
      void reply.status(401).send('Unauthorized')
      return
    }

    const invoiceId = Number(request.params.invoiceId)
    if (!Number.isFinite(invoiceId)) {
      void reply.status(400).send('Invalid invoice id')
      return
    }

    // Build user-scoped Supabase client (RLS enforced)
    const token = authHeader!.replace(/^Bearer\s+/i, '').trim()
    const userClient = createClient<Database>(fastify.config.SUPABASE_URL, process.env.SUPABASE_ANON_KEY ?? '', {
      global: { headers: { Authorization: `Bearer ${token}` } },
      auth: { autoRefreshToken: false, persistSession: false }
    })

    // Ensure the invoice is accessible (RLS). If not, return 403.
    const { data: inv, error } = await userClient
      .from('invoices')
      .select('id, entity_id, kind')
      .eq('id', invoiceId)
      .single() as { data: { id: number, entity_id: number, kind: string } | null, error: Error | null }

    if (error || !inv || inv.kind !== 'sale') {
      void reply.status(403).send('Forbidden')
      return
    }

    // Build input using user client to read entity/journal data under RLS
    const input = await invoiceToUblInput(userClient, invoiceId)
    const { xml, meta } = generateUblInvoice(input)

    // HTTP caching via ETag
    const etag = `W/\"${meta.hash}\"`
    const inm = request.headers['if-none-match']
    if (typeof inm === 'string' && inm === etag) {
      void reply.code(304).send()
      return
    }

    void reply.header('ETag', etag)
    void reply.header('Content-Type', 'application/xml; charset=utf-8')
    void reply.send(xml)
  }

  // New canonical path
  fastify.get('/invoices/:invoiceId/ubl', registerHandler)
  // Back-compat alias
  fastify.get('/ar/:invoiceId/ubl', registerHandler)
}

export default handler
