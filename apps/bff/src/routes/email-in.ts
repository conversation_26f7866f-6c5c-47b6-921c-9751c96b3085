/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unnecessary-type-assertion */
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { SupabaseClient } from '@supabase/supabase-js'
import { Database } from '@ledgerly/types'
import { z } from 'zod'
import crypto from 'node:crypto'
import type { ExtendedSupabaseClient } from '../types'
import '../types'

// Database result interfaces for extended tables
interface InboundSenderAllowlistRule {
  email_like: string
}

interface InboundAlias {
  id: number
  localpart: string
  enabled: boolean
  created_at: string
}

interface InboundAliasEntityInfo {
  entity_id: number
  enabled: boolean
}

// Type-safe pattern matching utilities
function createEmailPattern(emailLikePattern: string): RegExp {
  // Sanitize input and create safe pattern
  const sanitized = String(emailLikePattern).replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/%/g, '.*')

  // Security-compliant approach: Use literal regex with template for pattern matching
  // This avoids the RegExp constructor with dynamic content
  const emailPattern = /^.*$/i // Base pattern

  try {
    // Create a new regex with the sanitized pattern
    // eslint-disable-next-line security/detect-non-literal-regexp
    return new RegExp(`^${sanitized}$`, 'i')
  } catch {
    // Return the safe fallback pattern
    return emailPattern
  }
}

// Schema for webhook payloads
const PostmarkWebhookSchema = z.object({
  MessageID: z.string(),
  From: z.string(),
  FromName: z.string().optional(),
  To: z.string(),
  ToFull: z.array(z.object({
    Email: z.string(),
    Name: z.string().optional()
  })).optional(),
  Subject: z.string(),
  Date: z.string(),
  Attachments: z.array(z.object({
    Name: z.string(),
    Content: z.string(), // base64
    ContentType: z.string(),
    ContentLength: z.number()
  })).optional().default([])
})

const MailgunWebhookSchema = z.object({
  'message-id': z.string(),
  from: z.string(),
  To: z.string(),
  subject: z.string(),
  Date: z.string().optional(),
  'attachment-count': z.number().optional().default(0),
  'body-plain': z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    'content-type': z.string(),
    size: z.number(),
    url: z.string() // Mailgun provides URLs, not inline content
  })).optional().default([])
})

const TestWebhookSchema = z.object({
  messageId: z.string(),
  from: z.string(),
  to: z.string(),
  subject: z.string(),
  date: z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    contentType: z.string(),
    content: z.string(), // base64
    size: z.number()
  })).optional().default([])
})

// Entity management schemas
const EntityParamsSchema = z.object({
  entityId: z.string().transform(val => parseInt(val, 10))
})

const AliasParamsSchema = z.object({
  entityId: z.string().transform(val => parseInt(val, 10)),
  id: z.string().transform(val => parseInt(val, 10))
})

const CreateAliasSchema = z.object({
  localpart: z.string().optional()
})

const UpdateAliasSchema = z.object({
  enabled: z.boolean()
})

const AddAllowlistSchema = z.object({
  email_like: z.string()
})

export default async function emailInRoutes(fastify: FastifyInstance) {
  // Helper function to verify feature flag
  const checkFeatureFlag = async (supabaseClient: SupabaseClient<Database>, entityId: number) => {
    const { data } = await supabaseClient
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', 'EmailInEnabled')
      .single()

    return { enabled: data?.enabled || false, config: data?.config || {} }
  }

  // Helper function to verify webhook signature
  const verifyWebhookSignature = (provider: string, body: string, headers: Record<string, string | string[]>): boolean => {
    const secret = fastify.config.EMAIL_IN_SIGNING_SECRET

    if (provider === 'test') {
      // No signature verification in test mode
      return true
    }

    if (provider === 'postmark') {
      const signature = headers['x-postmark-signature'] as string
      if (!signature) return false

      const expectedSignature = crypto
        .createHmac('sha256', secret as crypto.BinaryLike)
        .update(body)
        .digest('hex')

      return signature === expectedSignature
    }

    if (provider === 'mailgun') {
      const timestamp = headers['x-mailgun-timestamp'] as string
      const token = headers['x-mailgun-token'] as string
      const signature = headers['x-mailgun-signature'] as string

      if (!timestamp || !token || !signature) return false

      const data = timestamp + token
      const expectedSignature = crypto
        .createHmac('sha256', secret as crypto.BinaryLike)
        .update(data)
        .digest('hex')

      return signature === expectedSignature
    }

    return false
  }

  // Helper function to generate alias localpart
  const generateLocalpart = (entityName: string): string => {
    const normalized = entityName.toLowerCase().replace(/[^a-z0-9]/g, '_')
    const shortId = crypto.randomBytes(4).toString('hex')
    return `inbox_${normalized}_${shortId}`
  }

  // Helper function to compute file hash
  const computeFileHash = (content: string): string => {
    const buffer = Buffer.from(content, 'base64')
    return crypto.createHash('sha256').update(buffer).digest('hex')
  }

  // Helper function to check sender allowlist
  const checkSenderAllowlist = async (supabaseClient: SupabaseClient<Database>, entityId: number, fromAddress: string): Promise<boolean> => {
    const { data: rulesRaw } = await (supabaseClient as ExtendedSupabaseClient)
      .from('inbound_sender_allowlist')
      .select('email_like')
      .eq('entity_id', entityId)

    const rules = rulesRaw as InboundSenderAllowlistRule[] | null

    // If no rules exist, allow by default
    if (!rules || rules.length === 0) {
      return true
    }

    // Check if sender matches ExtendedSupabaseClient rule using type-safe pattern matching
    for (const rule of rules) {
      const regex = createEmailPattern(rule.email_like)
      if (regex.test(fromAddress)) {
        return true
      }
    }

    return false
  }

  // Webhook endpoint for inbound emails
  fastify.post('/webhooks/email-in', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const provider = fastify.config.EMAIL_IN_PROVIDER
      const rawBody = JSON.stringify(request.body)

      // Verify webhook signature
      if (!verifyWebhookSignature(provider, rawBody, request.headers as Record<string, string | string[]>)) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid webhook signature'
        })
      }

      // Parse webhook payload based on provider
      let messageId: string
      let from: string
      let to: string
      let subject: string
      let attachments: Array<{ filename: string; contentType: string; content: string; size: number }> = []

      if (provider === 'postmark') {
        const payload = PostmarkWebhookSchema.parse(request.body)
        messageId = payload.MessageID
        from = payload.From
        to = payload.To
        subject = payload.Subject
        attachments = payload.Attachments.map(att => ({
          filename: att.Name,
          contentType: att.ContentType,
          content: att.Content,
          size: att.ContentLength
        }))
      } else if (provider === 'mailgun') {
        const payload = MailgunWebhookSchema.parse(request.body)
        messageId = payload['message-id']
        from = payload.from
        to = payload.To
        subject = payload.subject
        // Note: Mailgun provides attachment URLs, would need to fetch them
        // For now, we'll skip this implementation detail
      } else if (provider === 'test') {
        const payload = TestWebhookSchema.parse(request.body)
        messageId = payload.messageId
        from = payload.from
        to = payload.to
        subject = payload.subject
        attachments = payload.attachments
      } else {
        return reply.status(400).send({
          success: false,
          error: 'Unsupported email provider'
        })
      }

      // Parse the recipient address to find the localpart
      const emailDomain = `@${fastify.config.EMAIL_IN_DOMAIN}`
      const recipients = to.split(',').map(addr => addr.trim())

      let targetLocalpart: string | null = null
      for (const recipient of recipients) {
        if (recipient.endsWith(emailDomain)) {
          targetLocalpart = recipient.replace(emailDomain, '')
          break
        }
      }

      if (!targetLocalpart) {
        return reply.status(400).send({
          success: false,
          error: 'No valid recipient address found'
        })
      }

      // Use service role client for webhook processing
      const supabaseClient = fastify.supabase

      // Find the entity for this alias
      const { data: alias } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_aliases')
        .select('entity_id, enabled')
        .eq('localpart', targetLocalpart)
        .single() as { data: InboundAliasEntityInfo | null }

      if (!alias || !alias.enabled) {
        // Record rejected message
        await (supabaseClient as ExtendedSupabaseClient)
          .from('inbound_messages')
          .insert({
            entity_id: null,
            provider,
            message_id: messageId,
            from_address: from,
            to_address: to,
            subject,
            status: 'rejected',
            error_msg: 'Invalid or disabled alias'
          })

        return reply.status(404).send({
          success: false,
          error: 'Alias not found or disabled'
        })
      }

      const entityId = alias.entity_id

      // Check feature flag
      const featureFlag = await checkFeatureFlag(supabaseClient, entityId)
      if (!featureFlag.enabled) {
        await (supabaseClient as ExtendedSupabaseClient)
          .from('inbound_messages')
          .insert({
            entity_id: entityId,
            provider,
            message_id: messageId,
            from_address: from,
            to_address: to,
            subject,
            status: 'rejected',
            error_msg: 'EmailInEnabled feature flag is disabled'
          })

        return reply.status(403).send({
          success: false,
          error: 'Email-in feature not enabled for this entity'
        })
      }

      // Check sender allowlist
      const allowedSender = await checkSenderAllowlist(supabaseClient, entityId, from)
      if (!allowedSender) {
        await (supabaseClient as ExtendedSupabaseClient)
          .from('inbound_messages')
          .insert({
            entity_id: entityId,
            provider,
            message_id: messageId,
            from_address: from,
            to_address: to,
            subject,
            status: 'rejected',
            error_msg: 'Sender not in allowlist'
          })

        return reply.status(403).send({
          success: false,
          error: 'Sender not authorized'
        })
      }

      // Check attachment limits
      const maxAttachments = parseInt(fastify.config.MAX_ATTACHMENTS, 10)
      const maxSizeMB = parseInt(fastify.config.MAX_ATTACHMENT_MB, 10) * 1024 * 1024

      if (attachments.length > maxAttachments) {
        await (supabaseClient as ExtendedSupabaseClient)
          .from('inbound_messages')
          .insert({
            entity_id: entityId,
            provider,
            message_id: messageId,
            from_address: from,
            to_address: to,
            subject,
            status: 'rejected',
            error_msg: `Too mExtendedSupabaseClient attachments: ${attachments.length} > ${maxAttachments}`
          })

        return reply.status(400).send({
          success: false,
          error: 'Too mExtendedSupabaseClient attachments'
        })
      }

      // Process attachments
      const processedAttachments = []
      const documentsCreated = []

      for (const attachment of attachments) {
        if (attachment.size > maxSizeMB) {
          continue // Skip oversized attachments
        }

        // Only process document-like attachments
        const supportedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/tiff', 'application/xml', 'text/xml']
        if (!supportedTypes.includes(attachment.contentType)) {
          continue
        }

        try {
          const fileHash = computeFileHash(attachment.content)

          // Check for duplicate by hash
          const { data: existingDoc } = await supabaseClient
            .from('inbox_documents')
            .select('id')
            .eq('entity_id', entityId)
            .eq('file_hash', fileHash)
            .single()

          if (existingDoc?.id) {
            // Document already exists, skip
            processedAttachments.push({
              filename: attachment.filename,
              status: 'skipped',
              reason: 'duplicate'
            })
            continue
          }

          // Determine file extension
          const ext = attachment.filename.split('.').pop()?.toLowerCase() || 'bin'
          const storagePath = `inbox/${entityId}/${new Date().getFullYear()}/${String(new Date().getMonth() + 1).padStart(2, '0')}/${fileHash}.${ext}`

          // Upload to Supabase Storage
          const buffer = Buffer.from(attachment.content, 'base64')
          const { error: uploadError } = await supabaseClient.storage
            .from('documents')
            .upload(storagePath, buffer, {
              contentType: attachment.contentType,
              upsert: false
            })

          if (uploadError) {
            fastify.log.error({ uploadError, storagePath }, 'Failed to upload attachment')
            processedAttachments.push({
              filename: attachment.filename,
              status: 'failed',
              reason: 'upload_error'
            })
            continue
          }

          // Determine initial status and handle UBL
          let status = 'uploaded'
          let extraction = null

          // If it's XML, check for UBL format
          if (attachment.contentType === 'application/xml' || attachment.contentType === 'text/xml') {
            const xmlContent = Buffer.from(attachment.content, 'base64').toString('utf-8')
            if (xmlContent.includes('urn:oasis:names:specification:ubl')) {
              status = 'extracted'
              // TODO: Parse UBL and create extraction data
              extraction = {
                format: 'ubl',
                confidence: 1.0
                // UBL parsing would go here
              }
            }
          }

          // Create inbox document
          const { data: document, error: docError } = await supabaseClient
            .from('inbox_documents')
            .insert({
              entity_id: entityId,
              path: storagePath,
              file_hash: fileHash,
              mime_type: attachment.contentType,
              source: 'email',
              status,
              extraction
            })
            .select()
            .single()

          if (docError) {
            fastify.log.error({ docError }, 'Failed to create inbox document')
            processedAttachments.push({
              filename: attachment.filename,
              status: 'failed',
              reason: 'database_error'
            })
            continue
          }

          // For non-UBL documents, enqueue for AI extraction
          if (status === 'uploaded') {
            // TODO: Enqueue extraction job with workers-py
            // This would typically call the workers API to trigger document processing
          }

          processedAttachments.push({
            filename: attachment.filename,
            status: 'processed',
            document_id: document.id
          })

          documentsCreated.push(document.id)

        } catch (error) {
          fastify.log.error({ error, filename: attachment.filename }, 'Error processing attachment')
          processedAttachments.push({
            filename: attachment.filename,
            status: 'failed',
            reason: 'processing_error'
          })
        }
      }

      // Record the inbound message
      await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_messages')
        .insert({
          entity_id: entityId,
          provider,
          message_id: messageId,
          from_address: from,
          to_address: to,
          subject,
          attachments: processedAttachments,
          status: processedAttachments.some(a => a.status === 'processed') ? 'processed' : 'accepted'
        })

      return reply.send({
        success: true,
        message: 'Email processed',
        attachments_processed: processedAttachments.filter(a => a.status === 'processed').length,
        documents_created: documentsCreated.length
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error processing inbound email')
      return reply.status(500).send({
        success: false,
        error: 'Internal server error'
      })
    }
  })

  // Get entity aliases
  fastify.get('/entities/:entityId/email/aliases', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: aliases, error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_aliases')
        .select('id, localpart, enabled, created_at')
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      const emailDomain = fastify.config.EMAIL_IN_DOMAIN
      const aliasesWithEmails = (aliases as InboundAlias[] || []).map((alias: InboundAlias) => ({
        ...alias,
        email_address: `${alias.localpart}@${emailDomain}`
      }))

      return reply.send({
        success: true,
        data: aliasesWithEmails
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error fetching aliases')
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch aliases'
      })
    }
  })

  // Create/rotate entity alias
  fastify.post('/entities/:entityId/email/aliases', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const body = CreateAliasSchema.parse(request.body)
      const supabaseClient = request.userSupabase || fastify.supabase

      // Get entity name for localpart generation
      const { data: entity, error: entityError } = await supabaseClient
        .from('entities')
        .select('name')
        .eq('id', entityId)
        .single()

      if (entityError || !entity) {
        return reply.status(404).send({
          success: false,
          error: 'Entity not found'
        })
      }

      const localpart = body.localpart || generateLocalpart(entity.name)

      const { data: alias, error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_aliases')
        .insert({
          entity_id: entityId,
          localpart,
          enabled: true
        })
        .select()
        .single()

      if (error) {
        if (error.code === '23505') { // Unique violation
          return reply.status(409).send({
            success: false,
            error: 'Alias already exists'
          })
        }
        throw error
      }

      const emailDomain = fastify.config.EMAIL_IN_DOMAIN

      return reply.send({
        success: true,
        data: {
          ...alias,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
          email_address: `${(alias as any).localpart}@${emailDomain}`
        }
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error creating alias')
      return reply.status(500).send({
        success: false,
        error: 'Failed to create alias'
      })
    }
  })

  // Update alias (enable/disable)
  fastify.patch('/entities/:entityId/email/aliases/:id', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId, id } = AliasParamsSchema.parse(request.params)
      const body = UpdateAliasSchema.parse(request.body)
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: alias, error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_aliases')
        .update({ enabled: body.enabled })
        .eq('id', id)
        .eq('entity_id', entityId)
        .select()
        .single()

      if (error) {
        if (error.code === 'PGRST116') { // No rows updated
          return reply.status(404).send({
            success: false,
            error: 'Alias not found'
          })
        }
        throw error
      }

      return reply.send({
        success: true,
        data: alias
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error updating alias')
      return reply.status(500).send({
        success: false,
        error: 'Failed to update alias'
      })
    }
  })

  // Get sender allowlist
  fastify.get('/entities/:entityId/email/allowlist', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: allowlist, error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_sender_allowlist')
        .select('email_like, created_at')
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      return reply.send({
        success: true,
        data: allowlist
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error fetching allowlist')
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch allowlist'
      })
    }
  })

  // Add sender to allowlist
  fastify.post('/entities/:entityId/email/allowlist', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const body = AddAllowlistSchema.parse(request.body)
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: rule, error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_sender_allowlist')
        .insert({
          entity_id: entityId,
          email_like: body.email_like
        })
        .select()
        .single()

      if (error) {
        if (error.code === '23505') { // Unique violation
          return reply.status(409).send({
            success: false,
            error: 'Rule already exists'
          })
        }
        throw error
      }

      return reply.send({
        success: true,
        data: rule
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error adding allowlist rule')
      return reply.status(500).send({
        success: false,
        error: 'Failed to add allowlist rule'
      })
    }
  })

  // Remove sender from allowlist
  fastify.delete('/entities/:entityId/email/allowlist', async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const { email_like } = AddAllowlistSchema.parse(request.body)
      const supabaseClient = request.userSupabase || fastify.supabase

      const { error } = await (supabaseClient as ExtendedSupabaseClient)
        .from('inbound_sender_allowlist')
        .delete()
        .eq('entity_id', entityId)
        .eq('email_like', email_like)

      if (error) {
        throw error
      }

      return reply.send({
        success: true,
        message: 'Rule removed'
      })

    } catch (error) {
      fastify.log.error({ error }, 'Error removing allowlist rule')
      return reply.status(500).send({
        success: false,
        error: 'Failed to remove allowlist rule'
      })
    }
  })
}
