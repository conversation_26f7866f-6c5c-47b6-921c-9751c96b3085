/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback } from 'fastify'
import {
  BankTransactionStatusUpdateSchema,
  BankTransactionQuerySchema,
  BankTransactionLinkSchema,
  ApiResponse
} from '@ledgerly/types'
import { z } from 'zod'
import '../types' // Import type extensions

// Query parameter schemas
const EntityParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10))
})

const BankTransactionParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10)),
  transactionId: z.string().transform(val => parseInt(val, 10))
})

const bankTransactionRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // List bank transactions for an entity
  fastify.get('/entities/:id/bank-transactions', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const queryParams = BankTransactionQuerySchema.parse(request.query)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Build query
      let query = supabaseClient
        .from('bank_transactions')
        .select(`
          id,
          transaction_date,
          value_date,
          amount,
          counterparty_name,
          counterparty_account,
          description,
          reference,
          status,
          is_reconciled,
          currency,
          structured_ref,
          batch_id,
          bank_accounts!inner (
            id,
            name,
            entity_id
          )
        `)
        .eq('bank_accounts.entity_id', entityId)
        .order('transaction_date', { ascending: false })
        .order('created_at', { ascending: false })

      // Apply filters
      if (queryParams.status) {
        query = query.eq('status', queryParams.status)
      }
      if (queryParams.bank_account_id) {
        query = query.eq('bank_account_id', queryParams.bank_account_id)
      }
      if (queryParams.from_date) {
        query = query.gte('transaction_date', queryParams.from_date)
      }
      if (queryParams.to_date) {
        query = query.lte('transaction_date', queryParams.to_date)
      }

      // Apply pagination
      query = query.range(queryParams.offset, queryParams.offset + queryParams.limit - 1)

      const { data: transactions, error } = await query

      if (error) {
        throw new Error(`Failed to fetch bank transactions: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: transactions || []
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // Get a specific bank transaction
  fastify.get('/entities/:id/bank-transactions/:transactionId', async (request, reply) => {
    try {
      const { id: entityId, transactionId } = BankTransactionParamsSchema.parse(request.params)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: transaction, error } = await supabaseClient
        .from('bank_transactions')
        .select(`
          id,
          transaction_date,
          value_date,
          amount,
          counterparty_name,
          counterparty_account,
          description,
          reference,
          status,
          is_reconciled,
          currency,
          structured_ref,
          batch_id,
          raw_json,
          created_at,
          updated_at,
          bank_accounts!inner (
            id,
            name,
            entity_id
          ),
          bank_tx_links (
            id,
            kind,
            amount_applied,
            invoice_id,
            journal_id,
            created_at
          )
        `)
        .eq('id', transactionId)
        .eq('bank_accounts.entity_id', entityId)
        .single()

      if (error || !transaction) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank transaction not found'
        }
        return reply.code(404).send(response)
      }

      const response: ApiResponse = {
        success: true,
        data: transaction
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // Update bank transaction status
  fastify.patch('/entities/:id/bank-transactions/:transactionId/status', async (request, reply) => {
    try {
      const { id: entityId, transactionId } = BankTransactionParamsSchema.parse(request.params)
      const { status } = BankTransactionStatusUpdateSchema.parse(request.body)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // First verify the transaction exists and belongs to the entity
      const { data: existingTransaction, error: fetchError } = await supabaseClient
        .from('bank_transactions')
        .select(`
          id,
          status,
          bank_accounts!inner (
            entity_id
          )
        `)
        .eq('id', transactionId)
        .eq('bank_accounts.entity_id', entityId)
        .single()

      if (fetchError || !existingTransaction) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank transaction not found'
        }
        return reply.code(404).send(response)
      }

      // Validate status transition (basic business logic) - type-safe approach
      const getValidTransitions = (fromStatus: string): string[] => {
        switch (fromStatus) {
          case 'unmatched': return ['proposed', 'matched', 'failed']
          case 'proposed': return ['matched', 'unmatched', 'failed']
          case 'matched': return ['reconciled', 'unmatched', 'failed']
          case 'reconciled': return ['failed']
          case 'failed': return ['unmatched']
          default: return []
        }
      }

      const currentStatus = existingTransaction.status
      const allowedTransitions = currentStatus ? getValidTransitions(currentStatus) : []
      if (!currentStatus || !allowedTransitions.includes(status)) {
        const response: ApiResponse = {
          success: false,
          error: `Invalid status transition from '${currentStatus}' to '${status}'`
        }
        return reply.code(400).send(response)
      }

      // Update the status
      const { data: updatedTransaction, error: updateError } = await supabaseClient
        .from('bank_transactions')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)
        .select()
        .single()

      if (updateError) {
        throw new Error(`Failed to update bank transaction status: ${updateError.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: updatedTransaction
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // Create a link between bank transaction and invoice/journal
  fastify.post('/entities/:id/bank-transactions/:transactionId/links', async (request, reply) => {
    try {
      const { id: entityId, transactionId } = BankTransactionParamsSchema.parse(request.params)
      const linkData = BankTransactionLinkSchema.parse(request.body)

      // Validate that entity_id matches the route parameter
      if (linkData.entity_id !== entityId) {
        const response: ApiResponse = {
          success: false,
          error: 'Entity ID in request body must match route parameter'
        }
        return reply.code(400).send(response)
      }

      // Validate that bank_transaction_id matches the route parameter
      if (linkData.bank_transaction_id !== transactionId) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank transaction ID in request body must match route parameter'
        }
        return reply.code(400).send(response)
      }

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Verify the bank transaction exists and belongs to the entity
      const { data: transaction, error: transactionError } = await supabaseClient
        .from('bank_transactions')
        .select(`
          id,
          status,
          bank_accounts!inner (
            entity_id
          )
        `)
        .eq('id', transactionId)
        .eq('bank_accounts.entity_id', entityId)
        .single()

      if (transactionError || !transaction) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank transaction not found'
        }
        return reply.code(404).send(response)
      }

      // Validate that we have either invoice_id or journal_id based on kind
      if (linkData.kind === 'AR' || linkData.kind === 'AP') {
        if (!linkData.invoice_id) {
          const response: ApiResponse = {
            success: false,
            error: 'invoice_id is required for AR and AP links'
          }
          return reply.code(400).send(response)
        }
      } else if (linkData.kind === 'JOURNAL') {
        if (!linkData.journal_id) {
          const response: ApiResponse = {
            success: false,
            error: 'journal_id is required for JOURNAL links'
          }
          return reply.code(400).send(response)
        }
      }

      // Create the link
      const { data: newLink, error: linkError } = await supabaseClient
        .from('bank_tx_links')
        .insert({
          entity_id: linkData.entity_id,
          bank_transaction_id: linkData.bank_transaction_id,
          kind: linkData.kind,
          invoice_id: linkData.invoice_id ?? null,
          journal_id: linkData.journal_id ?? null,
          amount_applied: linkData.amount_applied
        })
        .select()
        .single()

      if (linkError) {
        throw new Error(`Failed to create bank transaction link: ${linkError.message}`)
      }

      // Update bank transaction status to 'matched' if it was 'unmatched' or 'proposed'
      if (transaction.status === 'unmatched' || transaction.status === 'proposed') {
        await supabaseClient
          .from('bank_transactions')
          .update({
            status: 'matched',
            updated_at: new Date().toISOString()
          })
          .eq('id', transactionId)
      }

      const response: ApiResponse = {
        success: true,
        data: newLink
      }

      return reply.code(201).send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // Get all links for a bank transaction
  fastify.get('/entities/:id/bank-transactions/:transactionId/links', async (request, reply) => {
    try {
      const { id: entityId, transactionId } = BankTransactionParamsSchema.parse(request.params)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const { data: links, error } = await supabaseClient
        .from('bank_tx_links')
        .select(`
          id,
          kind,
          amount_applied,
          invoice_id,
          journal_id,
          created_at,
          invoices (
            id,
            invoice_number,
            total_amount,
            status
          ),
          journals (
            id,
            reference,
            total_amount,
            status
          )
        `)
        .eq('entity_id', entityId)
        .eq('bank_transaction_id', transactionId)
        .order('created_at', { ascending: false })

      if (error) {
        throw new Error(`Failed to fetch bank transaction links: ${error.message}`)
      }

      const response: ApiResponse = {
        success: true,
        data: links || []
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // Delete a bank transaction link
  fastify.delete('/entities/:id/bank-transactions/:transactionId/links/:linkId', async (request, reply) => {
    try {
      const { id: entityId, transactionId } = BankTransactionParamsSchema.parse(request.params)
      const linkIdSchema = z.object({
        linkId: z.string().transform(val => parseInt(val, 10))
      })
      const { linkId } = linkIdSchema.parse(request.params)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Verify the link exists and belongs to the entity and transaction
      const { data: existingLink, error: fetchError } = await supabaseClient
        .from('bank_tx_links')
        .select('id')
        .eq('id', linkId)
        .eq('entity_id', entityId)
        .eq('bank_transaction_id', transactionId)
        .single()

      if (fetchError || !existingLink) {
        const response: ApiResponse = {
          success: false,
          error: 'Bank transaction link not found'
        }
        return reply.code(404).send(response)
      }

      // Delete the link
      const { error: deleteError } = await supabaseClient
        .from('bank_tx_links')
        .delete()
        .eq('id', linkId)

      if (deleteError) {
        throw new Error(`Failed to delete bank transaction link: ${deleteError.message}`)
      }

      // Check if there are any remaining links for this transaction
      const { data: remainingLinks, error: countError } = await supabaseClient
        .from('bank_tx_links')
        .select('id')
        .eq('bank_transaction_id', transactionId)

      if (!countError && (!remainingLinks || remainingLinks.length === 0)) {
        // No more links, update status back to 'unmatched'
        await supabaseClient
          .from('bank_transactions')
          .update({
            status: 'unmatched',
            updated_at: new Date().toISOString()
          })
          .eq('id', transactionId)
      }

      const response: ApiResponse = {
        success: true,
        data: { deleted: true }
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

}

export default bankTransactionRoutes
