import type { ARInvoiceInput } from '@ledgerly/domain-invoicing'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types'


// Type definitions for journal line data from Supabase
interface JournalLineData {
  id: number
  description: string | null
  debit_amount: string | null
  credit_amount: string | null
  accounts?: {
    id: number
    code: string
    name: string
    account_type: string
  } | null
  vat_code_id: number | null
  vat_codes?: {
    id: number
    rate: number
  } | null
}

export async function invoiceToUblInput(supabase: SupabaseClient<Database>, invoiceId: number): Promise<ARInvoiceInput> {
  const { data: invoice, error: invErr } = await supabase
    .from('invoices')
    .select('id, entity_id, kind, number, counterparty_name, counterparty_vat, invoice_date, due_date, total_amount, vat_amount, journal_id')
    .eq('id', invoiceId)
    .single()
  if (invErr || !invoice) throw new Error(`Invoice not found: ${invErr?.message ?? 'unknown'}`)
  if (invoice.kind !== 'sale') throw new Error('Only sale invoices are supported for UBL generation')

  const { data: entity } = await supabase
    .from('entities')
    .select('name')
    .eq('id', invoice.entity_id)
    .single()

  // Try reconstructing lines from journal_lines using revenue accounts
  let lines: ARInvoiceInput['lines'] = []
  if (invoice.journal_id) {
    const { data: jlData } = await supabase
      .from('journal_lines')
      .select('id, description, debit_amount, credit_amount, accounts!inner(id, code, name, account_type), vat_code_id, vat_codes:vat_code_id(id, rate)')
      .eq('journal_id', invoice.journal_id)
    const revenue = (jlData ?? []).filter((l: unknown): l is JournalLineData => {
      if (l && typeof l === 'object' && 'accounts' in l) {
        const item = l as JournalLineData
        return item.accounts?.account_type === 'revenue'
      }
      return false
    }) as JournalLineData[]
    lines = revenue.map((l: JournalLineData) => {
      const amount = Number(l.credit_amount ?? 0) - Number(l.debit_amount ?? 0)
      const vatRate = l.vat_codes?.rate ? Math.round(Number(l.vat_codes.rate) * 100) : 0
      return {
        id: l.id,
        description: l.description ?? l.accounts?.name ?? 'Line',
        quantity: '1',
        unitPrice: amount.toFixed(2),
        vatRate: (vatRate === 21 || vatRate === 12 || vatRate === 6) ? (vatRate) : 0,
        unit: 'C62'
      }
    })
  }

  // Fallback single line if no usable lines
  if (lines.length === 0) {
    const net = Number(invoice.total_amount) - Number(invoice.vat_amount)
    const rate = Number(invoice.vat_amount) > 0 && net > 0 ? Math.round((Number(invoice.vat_amount) / net) * 100) : 0
    const mappedRate = (rate === 21 || rate === 12 || rate === 6) ? rate : 0
    lines = [{ id: invoice.id, description: invoice.number, quantity: '1', unitPrice: net.toFixed(2), vatRate: mappedRate, unit: 'C62' }]
  }

  const input: ARInvoiceInput = {
    invoice: {
      id: invoice.id,
      number: invoice.number,
      issueDate: String(invoice.invoice_date),
      ...(invoice.due_date ? { dueDate: String(invoice.due_date) } : {}),
      currency: 'EUR'
    },
    seller: {
      name: entity?.name ?? 'Seller'
      // vatNumber/address optional at MVP
    },
    buyer: {
      name: invoice.counterparty_name,
      ...(invoice.counterparty_vat ? { vatNumber: invoice.counterparty_vat } : {})
    },
    lines
  }

  return input
}
