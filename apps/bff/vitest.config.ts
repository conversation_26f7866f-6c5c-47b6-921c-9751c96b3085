import { defineConfig } from 'vitest/config'
import path from 'node:path'

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@ledgerly/types': path.resolve(
        __dirname,
        '../../packages/types/src/index.ts'
      ),
      '@ledgerly/types/*': path.resolve(__dirname, '../../packages/types/src'),
      '@ledgerly/dal': path.resolve(
        __dirname,
        '../../packages/dal/src/index.ts'
      ),
      '@ledgerly/dal/*': path.resolve(__dirname, '../../packages/dal/src'),
      '@ledgerly/import-service': path.resolve(
        __dirname,
        '../../packages/import-service/src/index.ts'
      ),
      '@ledgerly/import-service/*': path.resolve(
        __dirname,
        '../../packages/import-service/src'
      ),
      '@ledgerly/domain-invoicing': path.resolve(
        __dirname,
        '../../packages/domain-invoicing/src/index.ts'
      ),
      '@ledgerly/domain-invoicing/*': path.resolve(
        __dirname,
        '../../packages/domain-invoicing/src'
      ),
      '@ledgerly/domain-bank': path.resolve(
        __dirname,
        '../../packages/domain-bank/src/index.ts'
      ),
      '@ledgerly/domain-bank/*': path.resolve(
        __dirname,
        '../../packages/domain-bank/src'
      ),
    },
  },
  test: {
    environment: 'node',
    globals: true,
    testTimeout: 30000,
    hookTimeout: 20000, // Extended hook timeout for app initialization
    // Use process pool to reduce memory pressure on CI workers
    pool: 'forks',
    poolOptions: { forks: { singleFork: true } },
    include: [
      'test/**/*.{test,spec}.{ts,tsx}',
      'src/**/*.{test,spec}.{ts,tsx}',
    ],
    exclude: ['node_modules/**/*', 'dist/**/*'],
    logHeapUsage: true,
    env: {
      NODE_ENV: 'test',
      SUPABASE_URL: process.env.SUPABASE_URL || 'http://localhost:54321',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || 'test-key',
      SUPABASE_SERVICE_KEY:
        process.env.SUPABASE_SERVICE_KEY || 'test-service-key',
      SUPABASE_SERVICE_ROLE_KEY:
        process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-role-key',
      INTERNAL_KEY: process.env.INTERNAL_KEY || 'test-internal-key',
      HOST: '0.0.0.0',
      PORT: '3001',
      BFF_PORT: '3001',
    },
  },
})
