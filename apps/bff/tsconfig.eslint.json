{"extends": "./tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@ledgerly/types": ["../../packages/types/src/index.ts"], "@ledgerly/types/*": ["../../packages/types/src/*"], "@ledgerly/dal": ["../../packages/dal/src/index.ts"], "@ledgerly/dal/*": ["../../packages/dal/src/*"], "@ledgerly/import-service": ["../../packages/import-service/src/index.ts"], "@ledgerly/import-service/*": ["../../packages/import-service/src/*"], "@ledgerly/domain-invoicing": ["../../packages/domain-invoicing/src/index.ts"], "@ledgerly/domain-invoicing/*": ["../../packages/domain-invoicing/src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.mts", "src/**/*.cts", "test/**/*.ts"]}