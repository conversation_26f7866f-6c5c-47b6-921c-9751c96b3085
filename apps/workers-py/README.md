# Workers-py: Python AI Workers Service

A comprehensive Python service for asynchronous document processing, OCR, and AI-powered analysis built with FastAPI, Celery, and modern Python libraries.

## Features

- **Document Processing**: Support for PDF and image files (PNG, JPG, JPEG, TIFF)
- **OCR Integration**: Text extraction using Tesseract OCR with preprocessing
- **PDF Processing**: Native text extraction with pdfplumber, fallback to OCR
- **AI Analysis**: Document categorization, entity extraction, and summarization
- **Async Processing**: Celery-based background task processing
- **Database Integration**: PostgreSQL with asyncpg for result storage
- **Modern Python**: Built with Python 3.11+, FastAPI, and Pydantic v2
- **Comprehensive Testing**: Full test suite with pytest and mocking
- **Docker Support**: Multi-stage Dockerfile with development and production targets
- **Development Tools**: Pre-configured with ruff, black, mypy, and uv package management

## Quick Start

### Prerequisites

- Python 3.11 or higher
- [uv](https://github.com/astral-sh/uv) for package management
- PostgreSQL database
- Redis for Celery broker
- Tesseract OCR (for image processing)

### Installation

1. **<PERSON><PERSON> and navigate to the project**:
   ```bash
   cd apps/workers-py
   ```

2. **Set up environment**:
   ```bash
   make env-setup
   # Edit .env file with your configuration
   ```

3. **Install dependencies**:
   ```bash
   make dev-install
   ```

4. **Initialize database**:
   ```bash
   make db-upgrade
   ```

### Running the Service

#### Development Mode

Start all services in development:
```bash
# Terminal 1: Start FastAPI server
make run-dev

# Terminal 2: Start Celery worker
make run-worker

# Terminal 3: (Optional) Start Celery beat scheduler
make run-beat
```

#### Using Docker Compose

```bash
# Start all services
docker-compose up

# With monitoring (includes Flower)
docker-compose --profile monitoring up
```

### Basic Usage

1. **Health Check**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Process a Document**:
   ```bash
   curl -X POST http://localhost:8000/process-document \
     -H "Content-Type: application/json" \
     -d '{
       "entity_id": "invoice-001",
       "file_url": "https://example.com/document.pdf",
       "options": {
         "ocr_language": "eng",
         "extract_tables": true,
         "ai_categorize": true
       }
     }'
   ```

3. **Check Task Status**:
   ```bash
   curl http://localhost:8000/tasks/{task_id}
   ```

## API Documentation

Once the service is running, visit:
- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc

For detailed documentation, configuration, and deployment instructions, see the full documentation in the source code.

## Development

### Available Commands

```bash
make dev-install      # Install development dependencies
make run-dev          # Run development server with reload
make run-worker       # Run Celery worker
make test             # Run full test suite
make lint             # Run code linting
make type             # Run type checking
make clean            # Clean temporary files
```

## License

MIT License - see LICENSE file for details.

---

**Workers-py** - Modern Python document processing service for the Ledgerly platform.
