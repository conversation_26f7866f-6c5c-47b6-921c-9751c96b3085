# Assist Mode Export Specification

## Overview

This specification defines the export functionality for entities operating in "Assist Mode", where <PERSON><PERSON><PERSON> processes accounting documents but exports the results to external accounting systems (WinBooks, Yuki, Exact Online, etc.) rather than maintaining local ledger entries.

## Business Context

### Operating Modes

**Ledger Mode**: Full accounting system with local double-entry bookkeeping
**Assist Mode**: Document processing and classification with export to external systems

### Assist Mode Workflow

```mermaid
graph TD
    A[Document Upload] --> B[OCR Processing]
    B --> C[AI Classification]  
    C --> D[User Review/Approval]
    D --> E[Generate Export]
    E --> F[Send to External System]
    F --> G[Mark as Exported]
```

## Export Formats

### 1. UBL (Universal Business Language) XML

Used for Belgian electronic invoicing and integration with accounting packages.

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
  <cbc:ID>INV-2024-001</cbc:ID>
  <cbc:IssueDate>2024-01-15</cbc:IssueDate>
  <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
  <!-- Full UBL structure -->
</Invoice>
```

**Use Cases:**
- Electronic invoicing compliance
- Direct import to Belgian accounting software
- B2B invoice exchange

### 2. CSV Export Format

Standardized CSV format compatible with most accounting systems.

```csv
Date,Reference,Account,Description,Debit,Credit,VATCode,VATAmount
2024-01-15,INV-001,4000,Sales Revenue,,100.00,BE21,21.00
2024-01-15,INV-001,1200,Accounts Receivable,121.00,,,
2024-01-15,INV-001,4510,VAT Payable,,21.00,,
```

**Use Cases:**
- Generic accounting system import
- Manual review and processing
- Backup and audit trails

### 3. System-Specific Formats

#### WinBooks Integration
- **Format**: WinBooks XML import format
- **Features**: Direct posting to specified dossier
- **Authentication**: API key or file-based transfer

#### Yuki Integration  
- **Format**: Yuki API JSON format
- **Features**: Real-time posting via REST API
- **Authentication**: OAuth 2.0

#### Exact Online Integration
- **Format**: Exact Web API calls
- **Features**: Direct GL transaction posting
- **Authentication**: OAuth 2.0 with refresh tokens

## Export Contracts

### Export Request Schema

```typescript
interface ExportRequest {
  entity_id: number
  period_start: string    // YYYY-MM-DD
  period_end: string      // YYYY-MM-DD
  format: 'ubl' | 'csv' | 'winbooks' | 'yuki' | 'exact'
  filter: {
    document_types?: ('invoice' | 'receipt' | 'bank')[]
    status?: ('processed' | 'approved' | 'exported')[]
    categories?: string[]
  }
  options: {
    include_zero_amounts?: boolean
    group_by_date?: boolean
    include_attachments?: boolean
    auto_send?: boolean
  }
  destination: {
    method: 'download' | 'email' | 'sftp' | 'api'
    target?: string  // email/SFTP/API endpoint
    credentials?: any // encrypted credentials
  }
}
```

### Export Response Schema

```typescript
interface ExportResponse {
  export_id: string
  status: 'processing' | 'completed' | 'failed' | 'sent'
  format: string
  record_count: number
  file_url?: string      // for downloads
  file_size?: number     // in bytes
  checksum?: string      // SHA-256
  created_at: string
  completed_at?: string
  error_message?: string
  transmission_log?: {
    method: string
    target: string
    sent_at: string
    response_code?: number
    response_message?: string
  }
}
```

## Data Mapping

### Document to Accounting Entry Mapping

```typescript
interface DocumentMapping {
  document_id: string
  document_type: 'purchase_invoice' | 'sales_invoice' | 'receipt' | 'bank_statement'
  
  // Extracted data
  extracted_data: {
    date: string
    reference: string
    counterparty: string
    total_amount: number
    vat_amount: number
    currency: 'EUR' | 'USD' | 'GBP'
  }
  
  // AI classifications
  classifications: {
    category: string        // e.g., "office_supplies", "utilities" 
    account_suggestion: string
    confidence: number
    vat_code: string
  }
  
  // Manual overrides
  manual_corrections: {
    account_code?: string
    category?: string
    description?: string
    amount_corrections?: {
      net_amount: number
      vat_amount: number
    }
  }
  
  // Export mappings
  export_mapping: {
    account_code: string    // Target system account
    cost_center?: string
    project_code?: string
    department?: string
  }
}
```

### Belgian Chart of Accounts Mapping

Standard Belgian account codes for common categories:

```typescript
const BELGIAN_ACCOUNT_MAPPING: Record<string, string> = {
  // Assets
  'cash': '5700',
  'bank': '5500', 
  'receivables': '4000',
  'inventory': '3000',
  
  // Liabilities  
  'payables': '4400',
  'vat_payable': '4510',
  'vat_receivable': '4110',
  
  // Income
  'sales': '7000',
  'services': '7100',
  
  // Expenses
  'office_supplies': '6800',
  'utilities': '6700',
  'rent': '6600',
  'fuel': '6540',
  'meals': '6560',
  'professional_services': '6210'
}
```

## Processing Rules

### Validation Rules

1. **Completeness Check**
   - All required fields present
   - Valid account codes for target system
   - Valid VAT codes and rates
   - Balanced entries (debits = credits)

2. **Business Logic Validation**
   - Dates within valid fiscal periods
   - Amounts within reasonable ranges
   - Currency consistency
   - No duplicate references

3. **System-Specific Validation**
   - Account codes exist in target system
   - VAT codes valid for target jurisdiction
   - User permissions for target entity

### Export Grouping Rules

```typescript
interface GroupingRules {
  group_by_date: boolean        // Group transactions by date
  group_by_supplier: boolean    // Group by counterparty
  group_by_category: boolean    // Group by expense category
  max_group_size: number        // Max transactions per group
  separate_vat_lines: boolean   // Separate VAT into own lines
}
```

### Error Handling

```typescript
interface ExportError {
  error_type: 'validation' | 'mapping' | 'transmission' | 'system'
  error_code: string
  message: string
  affected_records: string[]    // Document IDs with errors
  suggested_fix?: string
  retry_possible: boolean
}

// Common error codes:
const EXPORT_ERRORS = {
  INVALID_ACCOUNT: 'Account code not found in target system',
  MISSING_VAT_CODE: 'VAT code required but not specified',
  UNBALANCED_ENTRY: 'Debit and credit amounts do not balance',
  DUPLICATE_REFERENCE: 'Reference number already exists in target system',
  CONNECTION_FAILED: 'Unable to connect to target system',
  INSUFFICIENT_PERMISSIONS: 'User lacks permission for this operation'
}
```

## Integration Specifications

### SFTP Export

For systems requiring file-based integration:

```typescript
interface SFTPConfig {
  host: string
  port: number
  username: string
  password?: string        // encrypted
  private_key?: string     // encrypted SSH key
  directory: string        // target directory
  filename_pattern: string // e.g., "export_{entity}_{date}.csv"
  backup_directory?: string
  notification_email?: string
}
```

### API Integration

For real-time API-based exports:

```typescript
interface APIConfig {
  base_url: string
  authentication: {
    type: 'oauth2' | 'api_key' | 'basic'
    credentials: any        // encrypted
    token_url?: string
    scope?: string
  }
  endpoints: {
    transactions: string
    accounts: string
    validation: string
  }
  retry_policy: {
    max_attempts: number
    backoff_multiplier: number
    max_delay_seconds: number
  }
}
```

## Acceptance Tests

### Test Case 1: Basic CSV Export

**Given:**
- Entity in assist mode with 10 processed invoices
- Export request for last month, CSV format

**Expected:**
- CSV file generated with correct headers
- All 10 invoices included with proper mapping
- VAT calculations correct
- File downloadable within 30 seconds

### Test Case 2: UBL Electronic Invoice

**Given:**  
- Sales invoice document processed and approved
- Export request for UBL format

**Expected:**
- Valid UBL 2.1 XML generated
- All required Belgian e-invoicing fields present
- VAT rates and codes comply with Belgian standards
- XML validates against UBL schema

### Test Case 3: WinBooks Integration

**Given:**
- Entity configured with WinBooks API credentials
- 5 purchase invoices ready for export

**Expected:**
- Successful API connection to WinBooks
- All transactions posted to correct dossier
- Account mappings applied correctly
- Export marked as successful with transmission log

### Test Case 4: Export Error Handling

**Given:**
- Export request with invalid account mapping
- Some documents missing required fields

**Expected:**
- Export fails with detailed error report
- Specific documents and errors identified
- Suggested fixes provided
- No partial export created

### Test Case 5: Large Export Performance

**Given:**
- 1000 documents for export
- Various document types and formats

**Expected:**
- Export completes within 5 minutes
- Progress tracking available
- Memory usage remains stable
- File size limits respected

## Performance Requirements

- **Small Exports** (< 100 records): Complete within 30 seconds
- **Medium Exports** (100-1000 records): Complete within 5 minutes  
- **Large Exports** (> 1000 records): Stream processing with progress updates
- **Concurrent Exports**: Support 10 simultaneous exports per entity
- **File Size Limits**: 100MB per export file

## Security Requirements

### Data Protection
- All credentials encrypted at rest
- Export files encrypted during transmission
- Temporary files cleaned up after processing
- Audit trail for all export activities

### Access Control
- User must have 'export' permission for entity
- Rate limiting on export requests
- IP whitelisting for API integrations
- Two-factor authentication for sensitive exports

## Monitoring and Logging

### Metrics to Track
- Export success/failure rates by format
- Average processing time per export type
- File size distributions
- API integration response times
- User adoption of different export formats

### Audit Requirements
```sql
CREATE TABLE export_audit_log (
  id BIGSERIAL PRIMARY KEY,
  entity_id BIGINT NOT NULL,
  user_id UUID NOT NULL,
  export_id UUID NOT NULL,
  format TEXT NOT NULL,
  record_count INTEGER,
  file_size BIGINT,
  transmission_method TEXT,
  status TEXT NOT NULL,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Future Enhancements

### Phase 2 Features
- Real-time sync with external systems
- Bidirectional integration (import from external systems)
- Multi-currency support
- Custom mapping templates

### Phase 3 Features  
- Machine learning for improved account mapping
- Blockchain verification for export integrity
- Advanced reconciliation workflows
- Mobile app export capabilities