# Ledger Posting Specification

## Overview

This specification defines the contract and behavior for posting journal entries in the Ledgerly double-entry accounting system. It covers input validation, processing rules, invariants, and expected outputs.

## Business Rules

### Double-Entry Principles

1. **Balance Requirement**: Every journal entry must balance (Σ debits = Σ credits)
2. **Minimum Lines**: Every journal must have at least 2 lines
3. **Account Validity**: All referenced accounts must exist and be active
4. **Entity Scope**: All accounts must belong to the specified entity
5. **Precision**: All amounts must have exactly 2 decimal places
6. **Immutability**: Posted journal lines cannot be modified or deleted

### Account Types and Normal Balances

| Account Type | Normal Balance | Increase | Decrease |
|-------------|---------------|----------|----------|
| Asset | Debit | Debit | Credit |
| Expense | Debit | Debit | Credit |
| Liability | Credit | Credit | Debit |
| Equity | Credit | Credit | Debit |
| Revenue | Credit | Credit | Debit |

## API Contract

### Input Schema

```typescript
interface PostJournal {
  entity_id: number
  journal_type?: string // default: 'general'
  reference?: string    // optional reference number
  description: string   // required description
  transaction_date: string // YYYY-MM-DD format
  lines: JournalLine[]  // min 2 lines required
}

interface JournalLine {
  account_id: number
  description?: string
  debit_amount?: number  // exactly one of debit/credit required
  credit_amount?: number // exactly one of debit/credit required
  vat_code_id?: number   // optional VAT code reference
}
```

### Output Schema

```typescript
interface PostJournalResponse {
  journal_id: number
  is_balanced: boolean
  total_amount: number
  created_at: string
  line_count: number
}
```

### Error Responses

```typescript
interface ValidationError {
  code: string
  message: string
  field?: string
  details?: any
}

// Error codes:
// ENTITY_ACCESS_DENIED - user cannot access specified entity
// JOURNAL_UNBALANCED - debits ≠ credits
// INSUFFICIENT_LINES - less than 2 lines provided
// ACCOUNT_NOT_FOUND - referenced account doesn't exist
// ACCOUNT_INACTIVE - referenced account is not active
// INVALID_AMOUNT - amount is negative or has > 2 decimal places
// DUPLICATE_REFERENCE - journal reference already exists for entity/type
```

## Processing Flow

```mermaid
graph TD
    A[Request Received] --> B[Validate Entity Access]
    B --> C[Validate Input Schema]
    C --> D[Check Account Existence]
    D --> E[Calculate Balance]
    E --> F{Balanced?}
    F -->|No| G[Return Balance Error]
    F -->|Yes| H[Begin Transaction]
    H --> I[Create Journal Record]
    I --> J[Create Journal Lines]
    J --> K[Update Account Balances]
    K --> L[Log Audit Event]
    L --> M[Commit Transaction]
    M --> N[Return Journal ID]
```

## Validation Rules

### Pre-processing Validation

1. **Entity Access**: User must have accountant, bookkeeper, admin, or owner role for entity
2. **Schema Validation**: Input must conform to PostJournal schema
3. **Account Validation**: All account_ids must exist and be active for the entity
4. **Date Validation**: transaction_date must be valid date in YYYY-MM-DD format

### Balance Validation

```typescript
function validateBalance(lines: JournalLine[]): ValidationResult {
  const totalDebits = lines
    .filter(line => line.debit_amount !== undefined)
    .reduce((sum, line) => sum + line.debit_amount!, 0)
  
  const totalCredits = lines
    .filter(line => line.credit_amount !== undefined)  
    .reduce((sum, line) => sum + line.credit_amount!, 0)
  
  const difference = Math.abs(totalDebits - totalCredits)
  
  return {
    isValid: difference < 0.001, // Allow for floating point precision
    totalDebits,
    totalCredits,
    difference
  }
}
```

## Database Constraints

### Table Constraints

```sql
-- Journal lines must have exactly one of debit or credit
ALTER TABLE journal_lines 
ADD CONSTRAINT check_debit_or_credit 
CHECK (
  (debit_amount IS NOT NULL AND credit_amount IS NULL) OR 
  (debit_amount IS NULL AND credit_amount IS NOT NULL)
);

-- Amounts must be positive
ALTER TABLE journal_lines 
ADD CONSTRAINT check_positive_amounts 
CHECK (
  (debit_amount IS NULL OR debit_amount > 0) AND
  (credit_amount IS NULL OR credit_amount > 0)
);
```

### Trigger Constraints

```sql
-- Prevent modification of journal lines (append-only)
CREATE TRIGGER prevent_journal_line_modification
  BEFORE UPDATE OR DELETE ON journal_lines
  FOR EACH ROW 
  EXECUTE FUNCTION prevent_journal_line_modification();

-- Enforce journal balance (deferrable)
CREATE CONSTRAINT TRIGGER enforce_journal_balance
  AFTER INSERT OR UPDATE OR DELETE ON journal_lines
  DEFERRABLE INITIALLY DEFERRED
  FOR EACH ROW 
  EXECUTE FUNCTION check_journal_balance();
```

## Acceptance Tests

### Test Case 1: Valid Simple Journal

**Given:**
```json
{
  "entity_id": 1,
  "description": "Cash sale",
  "transaction_date": "2024-01-15",
  "lines": [
    {
      "account_id": 1001, // Cash account
      "description": "Cash received",
      "debit_amount": 100.00
    },
    {
      "account_id": 4001, // Sales revenue account  
      "description": "Sale of goods",
      "credit_amount": 100.00
    }
  ]
}
```

**Expected:**
- Journal created successfully
- journal_id returned
- is_balanced = true
- Both lines inserted with correct amounts
- Account balances updated

### Test Case 2: Unbalanced Journal

**Given:**
```json
{
  "entity_id": 1,
  "description": "Unbalanced entry",
  "transaction_date": "2024-01-15", 
  "lines": [
    {
      "account_id": 1001,
      "debit_amount": 100.00
    },
    {
      "account_id": 4001,
      "credit_amount": 75.00  // Unbalanced!
    }
  ]
}
```

**Expected:**
- HTTP 400 Bad Request
- Error code: JOURNAL_UNBALANCED
- Message: "Journal is not balanced: debits=100.00 credits=75.00 difference=25.00"
- No database changes

### Test Case 3: Complex Journal

**Given:**
```json
{
  "entity_id": 1,
  "description": "Invoice with VAT",
  "transaction_date": "2024-01-15",
  "lines": [
    {
      "account_id": 1200, // Accounts Receivable
      "description": "Invoice #INV-001",
      "debit_amount": 121.00
    },
    {
      "account_id": 4001, // Sales Revenue
      "description": "Sale of services",
      "credit_amount": 100.00
    },
    {
      "account_id": 2200, // VAT Payable
      "description": "VAT 21%",
      "credit_amount": 21.00,
      "vat_code_id": 1
    }
  ]
}
```

**Expected:**
- Journal created successfully with 3 lines
- Total debits = 121.00, total credits = 121.00
- VAT code properly linked

### Test Case 4: Access Denied

**Given:**
- User has no access to entity_id: 999
- Valid journal data for entity 999

**Expected:**
- HTTP 403 Forbidden
- Error code: ENTITY_ACCESS_DENIED
- No database changes

### Test Case 5: Invalid Account

**Given:**
```json
{
  "entity_id": 1,
  "description": "Invalid account test",
  "transaction_date": "2024-01-15",
  "lines": [
    {
      "account_id": 9999, // Non-existent account
      "debit_amount": 100.00
    },
    {
      "account_id": 4001,
      "credit_amount": 100.00
    }
  ]
}
```

**Expected:**
- HTTP 400 Bad Request
- Error code: ACCOUNT_NOT_FOUND
- Message: "Account 9999 does not exist or is not active for entity 1"

## Performance Requirements

- **Response Time**: 95% of requests complete within 200ms
- **Throughput**: Support 100 concurrent journal postings
- **Database Locks**: Minimize lock contention during posting
- **Index Usage**: All queries must use appropriate indexes

## Audit Requirements

Every journal posting must create audit records:

```sql
INSERT INTO audit_events (
  entity_id,
  table_name, 
  record_id,
  operation,
  new_values,
  user_id,
  created_at
) VALUES (
  journal.entity_id,
  'journals',
  journal.id,
  'INSERT', 
  journal_data_json,
  auth.uid(),
  NOW()
);
```

## Error Handling

### Rollback Scenarios

All database changes must be rolled back if:
- Any validation fails
- Balance constraint violation
- Database constraint violation
- System error during processing

### Idempotency

Journal posting should be idempotent when:
- Same reference number used for same entity/type
- Return existing journal_id instead of error
- No duplicate posting for identical requests

## Monitoring

Key metrics to track:
- Journal posting success rate
- Average response time
- Balance constraint violations
- Account access patterns
- Large journal entries (>100 lines)

## Integration Points

### Upstream Dependencies
- Entity membership validation
- Account existence verification
- VAT code validation
- User authentication

### Downstream Impacts
- Trial balance calculations
- Financial report generation
- Account balance updates
- Audit log entries