# LLM Coder Guide: Type Management & Database Access

This guide shows LLM coders how to work with types and database access in the Ledgerly project.

## 🎯 Golden Rules

### ✅ DO THIS
```typescript
// ✅ Use typed helpers from DAL
import { listUserEntities, signInWithEmail } from '@ledgerly/dal'

// ✅ Generate types from local DB for development
pnpm db:types

// ✅ Include SQL migration when using new schema
// In your PR: both the migration AND the code that uses it
```

### ❌ DON'T DO THIS
```typescript
// ❌ Don't use raw Supabase client
import { createClient } from '@supabase/supabase-js'

// ❌ Don't commit locally-generated types
git add packages/types/src/supabase.ts  // This will be blocked

// ❌ Don't use new schema without migration
// If your code uses a new column, include the SQL migration
```

## 🔄 Daily Workflow

### 1. Start Development
```bash
# Generate types from your local database
pnpm db:types

# Start development servers
pnpm dev
```

### 2. Make Database Changes
```bash
# 1. Create migration file
echo "ALTER TABLE entities ADD COLUMN new_field TEXT;" > packages/db/migrations/0004_add_new_field.sql

# 2. Apply migration locally
psql $DATABASE_URL -f packages/db/migrations/0004_add_new_field.sql

# 3. Regenerate types
pnpm db:types

# 4. Write code using new field
```

### 3. Create PR
```bash
# Your PR should include:
# - The SQL migration file
# - The code that uses new schema
# - Updated all.sql (if needed)

git add packages/db/migrations/0004_add_new_field.sql
git add apps/web/components/NewFeature.tsx
git commit -m "feat: add new field to entities"
```

## 🔌 Database Access Patterns

### Authentication
```typescript
import { 
  signInWithEmail, 
  signOut, 
  getUser, 
  onAuthStateChange 
} from '@ledgerly/dal'

// Sign in with magic link
await signInWithEmail('<EMAIL>')

// Get current user
const user = await getUser()

// Listen to auth changes
onAuthStateChange((event, session) => {
  console.log('Auth event:', event, session)
})
```

### Tenant Operations
```typescript
import { 
  listUserTenants, 
  createTenant,
  grantTenantRole 
} from '@ledgerly/dal'

// Get user's tenants
const tenants = await listUserTenants()

// Create new tenant (server-side)
const tenant = await createTenant({
  name: 'My Company',
  kind: 'SME'
})

// Grant tenant access
await grantTenantRole(tenantId, userId, 'tenant_admin')
```

### Entity Operations  
```typescript
import { 
  listUserEntities,
  createEntity,
  grantEntityRole,
  hasEntityRole
} from '@ledgerly/dal'

// Get user's entities
const entities = await listUserEntities()

// Create new entity
const entityId = await createEntity(tenantId, 'Client ABC', 'EUR')

// Check permissions
const canEdit = await hasEntityRole(entityId, userId, ['owner', 'admin'])

// Grant entity access
await grantEntityRole(entityId, userId, 'accountant')
```

### Invitations
```typescript
import { 
  createInvite,
  acceptInvite,
  listSentInvites 
} from '@ledgerly/dal'

// Invite user to entity
const token = await createInvite('entity', entityId.toString(), '<EMAIL>', 'viewer')

// Accept invitation  
await acceptInvite(token)

// List sent invites
const invites = await listSentInvites()
```

## 🏗️ Type System

### Getting Types
```typescript
// Import database types
import type { Database } from '@ledgerly/types/src/supabase'

// Extract table types
type Entity = Database['public']['Tables']['entities']['Row']
type EntityInsert = Database['public']['Tables']['entities']['Insert']

// Extract view types
type UserEntity = Database['public']['Views']['v_user_entities']['Row']
```

### Using Types with Components
```typescript
// components/EntityCard.tsx
import type { Database } from '@ledgerly/types/src/supabase'

type UserEntity = Database['public']['Views']['v_user_entities']['Row']

interface EntityCardProps {
  entity: UserEntity
}

export function EntityCard({ entity }: EntityCardProps) {
  return (
    <div>
      <h3>{entity.entity_name}</h3>
      <span>Role: {entity.role}</span>
    </div>
  )
}
```

## 🧪 Testing Your Changes

### Type Check
```bash
# Make sure your code compiles
pnpm typecheck
```

### Lint Check  
```bash
# Auto-fix common issues
pnpm lint --fix

# Check for remaining issues
pnpm lint
```

### Database Test
```bash
# Test your migration works
psql $DATABASE_URL -f packages/db/migrations/your_new_migration.sql

# Test RLS policies (if applicable)
psql $DATABASE_URL -f packages/db/tests/test_your_feature.sql
```

## 🚨 Common Mistakes & Fixes

### "Type errors after schema changes"
```bash
# Solution: Regenerate types
pnpm db:types
```

### "Pre-commit blocked my commit"  
```
❌ Blocking commit: supabase.ts was generated locally
```
```bash
# Solution: Restore the canonical file
git checkout packages/types/src/supabase.ts
```

### "Missing table/column errors"
```typescript
// Problem: Using new schema without migration
const entity = await supabase.from('entities').select('new_field')
```
```bash
# Solution: Add migration first
echo "ALTER TABLE entities ADD COLUMN new_field TEXT;" > packages/db/migrations/0004_add_field.sql
psql $DATABASE_URL -f packages/db/migrations/0004_add_field.sql
pnpm db:types
```

### "RLS policy violation"
```
Row-level security policy violation
```
```typescript
// Problem: User doesn't have access to entity
// Solution: Check membership first
const hasAccess = await hasEntityRole(entityId, userId, ['owner', 'admin'])
if (!hasAccess) {
  throw new Error('Access denied')
}
```

## 📋 Schema Evolution

### Safe Changes (Expand-Contract Pattern)

#### 1. Expand Phase
```sql
-- ✅ Add new column (nullable or with default)
ALTER TABLE entities ADD COLUMN new_field TEXT;

-- ✅ Add new table
CREATE TABLE new_feature (...);
```

#### 2. Switch Phase  
```typescript
// ✅ Update code to use new schema
const entities = await supabase
  .from('entities')
  .select('id, name, new_field')  // Start using new field
```

#### 3. Contract Phase (Later PR)
```sql
-- ✅ Remove old column after all code switched
ALTER TABLE entities DROP COLUMN old_field;
```

### Dangerous Changes (Don't Do)
```sql
-- ❌ Don't rename columns (breaks existing code)
ALTER TABLE entities RENAME COLUMN name TO entity_name;

-- ❌ Don't drop columns immediately
ALTER TABLE entities DROP COLUMN field_in_use;

-- ❌ Don't change column types destructively  
ALTER TABLE entities ALTER COLUMN id TYPE UUID;
```

## 🔗 Quick Reference

### Commands
```bash
pnpm db:types              # Generate types locally
pnpm db:migrate:local      # Apply migrations locally  
pnpm typecheck             # Check TypeScript
pnpm lint                  # Check and fix linting
pnpm dev                   # Start development servers
```

### Import Paths
```typescript
// Types
import type { Database } from '@ledgerly/types/src/supabase'

// Database helpers (USE THESE)
import { 
  signInWithEmail,
  listUserEntities,
  createInvite 
} from '@ledgerly/dal'

// Don't import raw Supabase
// import { createClient } from '@supabase/supabase-js' // ❌
```

### File Structure
```
packages/
├── types/src/supabase.ts     # Generated types (don't edit)
├── dal/src/                  # Database helpers (use these)
│   ├── auth.ts              # Authentication
│   ├── entities.ts          # Entity operations
│   ├── tenants.ts           # Tenant operations
│   └── invites.ts           # Invitation management
└── db/migrations/           # SQL migrations (add new ones)
```

---

**Remember**: Local types for speed, DAL helpers for safety, migrations for schema changes!