# Vercel Deployment Guide

## Overview

This guide covers the Vercel deployment setup for the Ledgerly monorepo. The project is configured to deploy the Next.js web application (`@ledgerly/web`) using proper monorepo support with pnpm and Turbo.

## Project Configuration

### Vercel Project Details
- **Project Name:** ledgerly
- **Project ID:** `prj_LVg6fLfdHk7yh5JFz7Zsca3H5fxb`
- **Organization:** jpkays-projects
- **Repository:** Connected to GitHub repository
- **Root Directory:** `apps/web` (configured in Vercel Dashboard)
- **Framework:** Next.js (auto-detected from apps/web/package.json)

### Deployment Configuration

The root `vercel.json` file contains the monorepo-specific build configuration:

```json
{
  "version": 2,
  "installCommand": "corepack enable && corepack prepare pnpm@9.0.0 --activate && pnpm install --frozen-lockfile",
  "buildCommand": "pnpm run build --filter=@ledgerly/web",
  "devCommand": "pnpm run dev --filter=@ledgerly/web",
  "outputDirectory": "apps/web/.next"
}
```

## Monorepo Setup

### Package Structure
- **Root Package:** `ledgerly-be` (manages monorepo with Turbo)
- **Web Package:** `@ledgerly/web` (Next.js application)
- **Dependencies:** Workspace packages (`@ledgerly/dal`, `@ledgerly/types`)

### Build Process
1. **Install Dependencies:** Uses pnpm with frozen lockfile for reproducible builds
2. **Build Command:** Leverages Turbo's `--filter` flag to build only the web app and its dependencies
3. **Output:** Next.js build artifacts in `apps/web/.next` directory

## Environment Variables

Ensure the following environment variables are configured in the Vercel Dashboard:

### Required Variables
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key (for server-side operations)
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret key
- `NEXTAUTH_URL` - Application URL for NextAuth.js callbacks

### Optional Variables
- `NODE_ENV` - Set to 'production' (usually auto-configured)
- `VERCEL_URL` - Auto-provided by Vercel
- Additional environment-specific variables as needed

## Deployment Process

### Automatic Deployments
- **Production:** Triggered on push to `main` branch
- **Preview:** Triggered on push to any branch or pull request
- **Build Time:** ~2-3 minutes for typical changes

### Manual Deployment
```bash
# Deploy to production
vercel deploy --prod

# Deploy preview
vercel deploy
```

## Troubleshooting

### Common Issues

1. **Framework Detection Issues**
   - **Cause:** Vercel cannot detect Next.js in root package.json
   - **Solution:** Ensure Root Directory is set to `apps/web` in Vercel Dashboard

2. **Workspace Dependencies Not Found**
   - **Cause:** Build process cannot resolve workspace packages
   - **Solution:** Verify pnpm is properly configured and workspace packages are built
   - **Check:** Ensure Turbo builds dependencies with `--filter=@ledgerly/web`

3. **Build Timeout**
   - **Cause:** Large monorepo or complex build process
   - **Solution:** Optimize build commands or contact Vercel support for increased limits

4. **Missing Environment Variables**
   - **Cause:** Required environment variables not configured
   - **Solution:** Configure all required variables in Vercel Dashboard → Settings → Environment Variables

### Build Command Debugging

If builds fail, check these components:

```bash
# Verify pnpm configuration
corepack enable && corepack prepare pnpm@9.0.0 --activate

# Test local build
pnpm install --frozen-lockfile
pnpm run build --filter=@ledgerly/web

# Check workspace dependencies
pnpm list --filter=@ledgerly/web

# Verify Turbo configuration
cat turbo.json
```

## Monitoring and Maintenance

### Performance Metrics
- **Build Time:** Monitor in Vercel Dashboard → Deployments
- **Bundle Size:** Check Next.js build output for size analysis
- **Core Web Vitals:** Monitor via Vercel Analytics or similar tools

### Regular Maintenance Tasks
1. **Update Dependencies:** Keep pnpm, Node.js, and packages updated
2. **Monitor Build Times:** Optimize if builds become slow
3. **Review Bundle Size:** Optimize imports and dependencies
4. **Environment Variables:** Rotate secrets regularly
5. **Deployment History:** Clean up old deployments if needed

## Security Considerations

### Best Practices
- **Environment Variables:** Never commit secrets to repository
- **Service Role Keys:** Restrict permissions to minimum required
- **Domain Configuration:** Ensure proper domain verification
- **HTTPS:** Always enforce HTTPS in production

### Access Control
- **Team Management:** Limit Vercel project access to necessary team members
- **Repository Access:** Ensure GitHub repository has appropriate permissions
- **API Keys:** Regular rotation of Supabase and other service keys

## CI/CD Integration

### GitHub Actions Integration
The project uses GitHub Actions for CI/CD with Vercel deployment:

1. **Build & Test:** GitHub Actions runs tests and linting
2. **Deploy:** Vercel automatically deploys on successful CI
3. **Status Checks:** Required status checks prevent broken deployments

### Workflow
```
Feature Branch → Pull Request → CI Tests → Manual Review → Merge → Vercel Deploy
```

## URLs and Access

### Production
- **Application URL:** `https://ledgerly-jpkays-projects.vercel.app`
- **Custom Domain:** Configure in Vercel Dashboard → Settings → Domains

### Dashboard Access
- **Vercel Dashboard:** https://vercel.com/jpkays-projects/ledgerly
- **Deployment History:** Available in dashboard
- **Function Logs:** Real-time logs for API routes and functions

## Support and Resources

### Documentation
- [Vercel Monorepo Documentation](https://vercel.com/docs/concepts/git#monorepos)
- [Next.js Deployment on Vercel](https://nextjs.org/docs/deployment)
- [pnpm Workspace Guide](https://pnpm.io/workspaces)

### Contact
- **Vercel Support:** https://vercel.com/help
- **Internal Issues:** Create issue in project repository

---

**Last Updated:** September 2025  
**Configuration Version:** v2 (monorepo-optimized)