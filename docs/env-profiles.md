# Environment Profiles

This document defines the three environments for Ledgerly development and deployment.

## 🏗️ Environment Architecture

```
┌─────────────────┐  ┌──────────────────┐  ┌─────────────────┐
│  Local Dev      │  │  Staging (EU)    │  │  Production (EU)│
│  (Supabase CLI) │  │  (Remote)        │  │  (Remote)       │
└─────────────────┘  └──────────────────┘  └─────────────────┘
        ↑                       ↑                      ↑
    Developers              Maintainers +           Maintainers
                               CI/CD                   Only
```

## 📋 Quick Reference

| Environment | Purpose | Access | Supabase Project |
|-------------|---------|--------|------------------|
| **Local** | Development | All LLM Coders | CLI only (no remote project) |
| **Staging** | QA, Demos, Type Gen | Maintainers + CI | `ledgerly-be-staging` |
| **Production** | Live Users | Maintainers Only | `ledgerly-be-prod` |

## 🚀 Local Development

**For LLM Coders and Contributors**

### Setup (One-time)
```bash
# 1. Clone and enter any worktree
git worktree add ../wt/your-feature -b feat/your-feature
cd ../wt/your-feature

# 2. Install dependencies
pnpm install
uv venv && uv sync  # Python workers

# 3. Start Supabase locally
supabase start

# 4. Copy environment variables
cp .env.example .env.local
# ✅ .env.local is already configured with correct local values

# 5. Generate database types
pnpm db:types

# 6. Start development servers
pnpm dev
```

### Environment Variables (Local)
```bash
# Public (browser-safe)
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Server-only (BFF, Workers, Tests)
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
```

### Services Available
- **API**: http://127.0.0.1:54321
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres  
- **Studio**: http://127.0.0.1:54323
- **Email**: http://127.0.0.1:54324 (Inbucket - catch-all email)

### Daily Commands
```bash
# Start everything
supabase start
pnpm dev

# Apply database migrations
psql "$DATABASE_URL" -f packages/db/migrations/all.sql

# Run tests
pnpm test                    # JS/TS tests
psql "$DATABASE_URL" -f packages/db/tests/001_invariants.sql  # pgTAP

# Generate types (from local DB)
pnpm db:types
```

## 🧪 Staging Environment

**For Maintainers and CI/CD**

### Project Details
- **Name**: `ledgerly-be-staging`
- **Region**: EU (Europe)
- **Purpose**: QA, partner demos, type generation for main branch

### Environment Variables (Staging)
```bash
# Managed via GitHub Secrets and Vercel/Deploy Platform
STAGING_SUPABASE_URL=https://[project-ref].supabase.co
STAGING_SUPABASE_ANON_KEY=[staging-anon-key]
STAGING_SUPABASE_SERVICE_ROLE_KEY=[staging-service-role]
```

### CI/CD Commands (Maintainers)
```bash
# Link to staging (in CI)
supabase link --project-ref $STAGING_SUPABASE_PROJECT_REF

# Apply migrations to staging
supabase db push

# Generate types from staging
supabase gen types typescript --linked > packages/types/src/supabase.ts

# Drift check (ensure staging matches Git)
supabase db diff --linked
```

### Storage Buckets (Staging)
- `invoices` - Document storage
- `exports` - Generated reports  
- `temp` - Temporary files (shorter retention)

## 🚀 Production Environment

**For Live Users - Maintainers Only**

### Project Details  
- **Name**: `ledgerly-be-prod`
- **Region**: EU (Europe)
- **Purpose**: Live customer data and operations

### Security & Access
- Service role keys rotated regularly
- No direct database access (migrations only via approved releases)
- All changes go through staging first
- pgAudit enabled for compliance

### Release Process
```bash
# 1. Staging approval ✅
# 2. Link to production (release job only)
supabase link --project-ref $PROD_SUPABASE_PROJECT_REF

# 3. Apply same migrations
supabase db push

# 4. Smoke tests
curl https://api.ledgerly.com/healthz
```

## 🛡️ Security Guardrails

### ✅ Safe Practices
- **Local**: Use CLI-generated keys only
- **Staging**: Accessible via CI + maintainer PATs
- **Production**: Release pipeline only
- **RLS**: Default-deny on all entity-scoped tables
- **Migrations**: Forward-only, never edit old files
- **Secrets**: Never in Git, platform env vars only

### ❌ Never Do This
- Use production keys locally
- Edit old migration files
- Create tables via Supabase dashboard UI
- Store real keys in .env files committed to Git
- Bypass RLS with service role in browser code

## 🔧 Environment Variables by Component

### Next.js (apps/web)
```bash
# Browser + Server
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
APP_ENV=local|staging|prod
```

### BFF (apps/bff)  
```bash
# Server only
SUPABASE_URL=
SUPABASE_SERVICE_ROLE_KEY=
DATABASE_URL=
BFF_PORT=4000
```

### Workers (apps/workers-py)
```bash
# Server only  
SUPABASE_URL=
SUPABASE_SERVICE_ROLE_KEY=
REDIS_URL=
```

## 🚨 Troubleshooting

### "Cannot connect to Supabase"
```bash
# Check if Supabase is running
supabase status

# Restart if needed
supabase stop && supabase start
```

### "RLS policy violation"
- Check that user has membership in `entity_memberships`
- Verify `current_entity_id` is set in session
- Test RLS policies in local Studio

### "Type errors after DB changes"
```bash
# Regenerate types from local DB
pnpm db:types

# Or from staging (maintainers)
supabase gen types typescript --linked > packages/types/src/supabase.ts
```

### Port Conflicts (Multiple Worktrees)
Edit `.env.local` in one worktree:
```bash
BFF_PORT=4001
# Change web port in package.json dev script
```

## 📚 Related Documentation

- [Supabase CLI Documentation](https://supabase.com/docs/guides/cli)
- [RLS Policies Guide](../packages/db/README.md)
- [Deployment Guide](./deployment.md)
- [Testing Guide](./testing.md)

---

**🔑 Key Takeaway**: LLM Coders work locally only. Maintainers manage staging/prod. Schema lives in Git, not dashboards.