import { createHash, randomUUID } from 'crypto'
import { parseCoda, type ParsedBatch } from '@ledgerly/domain-bank'
import type { NormalizedTx, ImportResult } from '../types/normalized-tx'

type ParsedTransaction = {
  bookingDate: string
  valueDate?: string
  amount: string
  transactionCode?: string
  reference?: string
  structuredRef?: string
  counterparty?: string
  counterpartyIban?: string
  raw: Record<string, unknown>[]
}

// Safe utility functions for unknown data access
function validateAndCastParsedBatch(result: unknown): ParsedBatch {
  if (!result || typeof result !== 'object') {
    throw new Error('Invalid parser result: not an object')
  }

  // Safe property extraction with validation
  const entries = safeArrayAccess<ParsedTransaction>(result, 'entries')
  const warnings = safeArrayAccess<string>(result, 'warnings')
  const currency = safeStringProperty(result, 'currency')
  const accountIban = safeStringProperty(result, 'accountIban')
  const openingBalance = safeStringProperty(result, 'openingBalance')
  const closingBalance = safeStringProperty(result, 'closingBalance')
  const statementDate = safeStringProperty(result, 'statementDate')
  const statementCount = safeNumberProperty(result, 'statementCount')

  return {
    entries,
    warnings,
    currency,
    accountIban,
    openingBalance,
    closingBalance,
    statementDate,
    statementCount,
  }
}

function safeStringAccess(value: unknown, fallback = ''): string {
  return typeof value === 'string' ? value : fallback
}

function safeArrayAccess<T>(obj: unknown, key: string): T[] {
  if (!obj || typeof obj !== 'object') return []
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return Array.isArray(value) ? (value as T[]) : []
}

function safeStringProperty(obj: unknown, key: string): string | undefined {
  if (!obj || typeof obj !== 'object') return undefined
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return typeof value === 'string' ? value : undefined
}

function safeNumberProperty(obj: unknown, key: string): number | undefined {
  if (!obj || typeof obj !== 'object') return undefined
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return typeof value === 'number' ? value : undefined
}

function safeNumberParse(value: unknown, fallback = 0): number {
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? fallback : parsed
  }
  return typeof value === 'number' && !isNaN(value) ? value : fallback
}

export class ImportService {
  importFile(
    fileContent: string,
    format: 'coda' | 'csv',
    _filename?: string
  ): ImportResult {
    const batchId = randomUUID()

    try {
      // Get the parsed and validated batch based on format
      const validatedBatch: ParsedBatch = this.getParsedBatch(
        fileContent,
        format
      )

      // Extract validated data - all properties are guaranteed to be properly typed
      const normalizedTxs = this.normalizeTransactions(
        validatedBatch.entries || [],
        validatedBatch.currency
      )

      // Build result with validated properties
      const result: ImportResult = {
        batchId,
        summary: {
          imported: normalizedTxs.length,
          skipped: 0,
          deduped: 0, // Will be handled at database level
        },
        entries: normalizedTxs,
        warnings: validatedBatch.warnings || [],
        ...(validatedBatch.accountIban
          ? { accountIban: validatedBatch.accountIban }
          : {}),
        ...(validatedBatch.openingBalance
          ? { openingBalance: validatedBatch.openingBalance }
          : {}),
        ...(validatedBatch.closingBalance
          ? { closingBalance: validatedBatch.closingBalance }
          : {}),
      }

      return result
    } catch (error) {
      throw new Error(
        `Failed to import ${format} file: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  private getParsedBatch(
    fileContent: string,
    format: 'coda' | 'csv'
  ): ParsedBatch {
    switch (format) {
      case 'coda': {
        // Parse and validate CoDA format
        try {
          // Cast to unknown to ensure TypeScript treats this as completely untyped
          const rawResult: unknown = parseCoda(fileContent)
          // Validate and return properly typed result
          return validateAndCastParsedBatch(rawResult)
        } catch (error) {
          throw new Error(
            `Failed to parse CoDA file: ${error instanceof Error ? error.message : 'Unknown error'}`
          )
        }
      }
      case 'csv':
        // TODO: Implement CSV parser when available
        throw new Error('CSV format not yet implemented')
      default:
        throw new Error(`Unsupported format: ${format as string}`)
    }
  }

  private normalizeTransactions(
    entries: ParsedTransaction[],
    currency?: string
  ): NormalizedTx[] {
    if (!Array.isArray(entries)) {
      throw new Error('Invalid entries array provided')
    }

    return entries.map(entry => {
      if (!entry || typeof entry !== 'object') {
        throw new Error('Invalid transaction entry')
      }

      const bookingDate = safeStringAccess(entry.bookingDate)
      const valueDate = safeStringAccess(entry.valueDate, bookingDate)
      const amount = safeNumberParse(entry.amount)

      if (!bookingDate) {
        throw new Error('Transaction missing required booking date')
      }

      const normalizedTx: NormalizedTx = {
        transaction_date: bookingDate,
        value_date: valueDate || bookingDate,
        amount,
        counterparty_name: safeStringAccess(entry.counterparty),
        description: safeStringAccess(entry.reference),
        reference: safeStringAccess(entry.structuredRef),
        transaction_id: this.generateTransactionId(entry),
        dedupe_hash: this.computeDedupeHash(entry),
        currency: safeStringAccess(currency, 'EUR'),
        structured_ref: safeStringAccess(entry.structuredRef),
        raw_json: { raw: entry.raw || {} },
      }

      return normalizedTx
    })
  }

  private generateTransactionId(entry: ParsedTransaction): string {
    // Create deterministic ID from key fields with safe access
    const keyData = [
      safeStringAccess(entry.bookingDate),
      safeStringAccess(entry.valueDate, safeStringAccess(entry.bookingDate)),
      safeStringAccess(entry.amount),
      safeStringAccess(entry.reference),
      safeStringAccess(entry.counterparty),
    ].join('|')

    return createHash('sha256').update(keyData).digest('hex').substring(0, 16)
  }

  private computeDedupeHash(entry: ParsedTransaction): string {
    // Create hash for deduplication using key identifying fields with safe access
    const dedupeData = [
      safeStringAccess(entry.bookingDate),
      safeStringAccess(entry.valueDate, safeStringAccess(entry.bookingDate)),
      safeStringAccess(entry.amount),
      safeStringAccess(entry.reference),
      safeStringAccess(entry.counterparty),
    ].join('|')

    return createHash('sha256').update(dedupeData).digest('hex')
  }
}

export const importService = new ImportService()
