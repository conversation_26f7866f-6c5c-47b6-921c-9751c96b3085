{"name": "@ledgerly/import-service", "version": "0.1.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc -b", "typecheck": "tsc -p tsconfig.typecheck.json", "test": "vitest run --passWithNoTests", "lint": "eslint src --ext .ts"}, "dependencies": {"@ledgerly/domain-bank": "workspace:*", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "peerDependencies": {"typescript": "^5.0.0"}}