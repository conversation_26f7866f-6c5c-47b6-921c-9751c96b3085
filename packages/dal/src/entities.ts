import { createBrowserClient } from './client'
import type { Database } from '@ledgerly/types'

type Entity = Database['public']['Tables']['entities']['Row']
type UserEntity = Database['public']['Views']['v_user_entities']['Row']

/**
 * Get all entities accessible to the current user
 */
export async function listUserEntities(userId?: string) {
  const client = createBrowserClient()

  let query = client.from('v_user_entities').select('*')

  if (userId) {
    query = query.eq('user_id', userId)
  }

  const { data, error } = await query

  if (error) throw error
  return data as UserEntity[]
}

/**
 * Get a specific entity by ID
 */
export async function getEntity(entityId: number) {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('entities')
    .select('*')
    .eq('id', entityId)
    .single()

  if (error) throw error
  return data as Entity
}

/**
 * Create a new entity (requires tenant admin)
 */
export async function createEntity(
  tenantId: number,
  name: string,
  currency = 'EUR'
) {
  const client = createBrowserClient()

  const { data, error } = await client.rpc('rpc_create_entity', {
    p_tenant_id: tenantId,
    p_name: name,
    p_currency: currency,
  })

  if (error) throw error
  return data // Returns entity ID
}

/**
 * Check if user has specific role in entity
 */
export async function hasEntityRole(
  entityId: number,
  userId: string,
  roles: string[]
): Promise<boolean> {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('entity_memberships')
    .select('role')
    .eq('entity_id', entityId)
    .eq('user_id', userId)
    .in('role', roles)
    .maybeSingle()

  if (error) throw error
  return !!data
}

/**
 * Grant entity role to user (requires entity admin or tenant admin)
 */
export async function grantEntityRole(
  entityId: number,
  targetUserId: string,
  role: string
) {
  const client = createBrowserClient()

  const { error } = await client.rpc('rpc_grant_entity_role', {
    p_entity: entityId,
    p_user: targetUserId,
    p_role: role,
  })

  if (error) throw error
}

/**
 * List entity memberships for an entity
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function listEntityMemberships(entityId: number): Promise<any[]> {
  const client = createBrowserClient()

  const { data, error } = await client
    .from('entity_memberships')
    .select(
      `
      *,
      user:user_id (
        email,
        raw_user_meta_data
      )
    `
    )
    .eq('entity_id', entityId)

  if (error) throw error
  return data
}
