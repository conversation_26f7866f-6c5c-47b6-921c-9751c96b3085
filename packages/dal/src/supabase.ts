import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { Database } from '@ledgerly/types'

export interface SupabaseConfig {
  url: string
  anonKey: string
  serviceRoleKey?: string
}

let config: SupabaseConfig | null = null
let anonClient: SupabaseClient<Database> | null = null
let serviceClient: SupabaseClient<Database> | null = null

export function initializeSupabase(supabaseConfig: SupabaseConfig): void {
  config = supabaseConfig
  
  // Create anonymous client (for authenticated users)
  anonClient = createClient<Database>(
    supabaseConfig.url,
    supabaseConfig.anonKey,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true
      }
    }
  )
  
  // Create service role client (for server-side operations)
  if (supabaseConfig.serviceRoleKey) {
    serviceClient = createClient<Database>(
      supabaseConfig.url,
      supabaseConfig.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
  }
}

export function getAnonClient(): SupabaseClient<Database> {
  if (!anonClient) {
    throw new Error('Supabase not initialized. Call initializeSupabase() first.')
  }
  return anonClient
}

export function getServiceClient(): SupabaseClient<Database> {
  if (!serviceClient) {
    throw new Error('Service role client not available. Ensure serviceRoleKey is provided.')
  }
  return serviceClient
}

export function createUserClient(accessToken: string): SupabaseClient<Database> {
  if (!config) {
    throw new Error('Supabase not initialized. Call initializeSupabase() first.')
  }
  
  return createClient<Database>(
    config.url,
    config.anonKey,
    {
      global: {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      },
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}