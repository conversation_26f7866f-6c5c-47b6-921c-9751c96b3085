/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { SupabaseClient } from '@supabase/supabase-js'
import type {
  VATPreviewRow,
  VATPreviewParams,
  VATExportParams,
  VATError,
  VATPreviewResponse,
  VATSummary,
} from '@ledgerly/types'
import { isValidVATDateRange } from '@ledgerly/types'
import type { Database } from './client'

// Safe helpers for unknown values
function safeNumber(value: unknown): number {
  if (typeof value === 'number' && Number.isFinite(value)) return value
  const n = parseFloat(String(value))
  return Number.isFinite(n) ? n : 0
}

function safeString(value: unknown, fallback = ''): string {
  return typeof value === 'string' ? value : fallback
}

function safeDirection(value: unknown): 'input' | 'output' {
  return value === 'input' || value === 'output' ? value : 'output'
}

function extractErrorCode(err: unknown, fallback: string): string {
  if (err && typeof err === 'object' && 'code' in err) {
    const codeVal = (err as { code?: unknown }).code
    return typeof codeVal === 'string' ? codeVal : String(codeVal)
  }
  return fallback
}

/**
 * Typed wrapper for rpc_vat_preview RPC function
 * Returns VAT preview data for a given entity and period
 */
export async function rpcVatPreview(
  userClient: SupabaseClient<Database>,
  params: VATPreviewParams
): Promise<VATPreviewResponse> {
  // Validate date format
  if (!isValidVATDateRange({ start: params.p_start, end: params.p_end })) {
    const error: VATError = new Error('Invalid date format. Use YYYY-MM-DD')
    error.code = 'INVALID_DATE_FORMAT'
    throw error
  }

  type VatPreviewRawRow = {
    grid_code?: unknown
    direction?: unknown
    base_total?: unknown
    vat_total?: unknown
  }
  const { data, error } = await (userClient as unknown as AnyRpcClient).rpc(
    'rpc_vat_preview',
    {
      p_entity: params.p_entity,
      p_start: params.p_start,
      p_end: params.p_end,
    }
  )

  if (error) {
    const vatError: VATError = new Error(
      `Failed to generate VAT preview: ${error instanceof Error ? error.message : 'Unknown error'}`
    )
    vatError.code = extractErrorCode(error, 'VAT_PREVIEW_ERROR')
    vatError.details = error as unknown
    throw vatError
  }

  // Cast to our typed interface - safe until next typegen
  const vatPreviewArray = Array.isArray(data)
    ? (data as VatPreviewRawRow[])
    : []
  const processedEntries = vatPreviewArray.map((row): VATPreviewRow => {
    return {
      grid_code: safeString(row.grid_code, 'UNMAPPED'),
      direction: safeDirection(row.direction),
      base_total: safeNumber(row.base_total),
      vat_total: safeNumber(row.vat_total),
    }
  })

  // Calculate summary totals
  let totalBaseOutput = 0
  let totalVATOutput = 0
  let totalBaseInput = 0
  let totalVATInput = 0

  processedEntries.forEach(entry => {
    if (entry.direction === 'output') {
      totalBaseOutput += entry.base_total
      totalVATOutput += entry.vat_total
    } else if (entry.direction === 'input') {
      totalBaseInput += entry.base_total
      totalVATInput += entry.vat_total
    }
  })

  const summary: VATSummary = {
    output: {
      base_total: totalBaseOutput,
      vat_total: totalVATOutput,
    },
    input: {
      base_total: totalBaseInput,
      vat_total: totalVATInput,
    },
    net_vat_payable: totalVATOutput - totalVATInput,
  }

  return {
    period: {
      start: params.p_start,
      end: params.p_end,
    },
    entries: processedEntries,
    summary,
  }
}

/**
 * Typed wrapper for rpc_vat_export_csv RPC function
 * Returns CSV export data for VAT entries
 */
export async function rpcVatExportCsv(
  userClient: SupabaseClient<Database>,
  params: VATExportParams
): Promise<string> {
  // Validate date format
  if (!isValidVATDateRange({ start: params.p_start, end: params.p_end })) {
    const error: VATError = new Error('Invalid date format. Use YYYY-MM-DD')
    error.code = 'INVALID_DATE_FORMAT'
    throw error
  }

  const { data, error } = await (userClient as unknown as AnyRpcClient).rpc(
    'rpc_vat_export_csv',
    {
      p_entity: params.p_entity,
      p_start: params.p_start,
      p_end: params.p_end,
    }
  )

  if (error) {
    const vatError: VATError = new Error(
      `Failed to generate VAT CSV export: ${error instanceof Error ? error.message : 'Unknown error'}`
    )
    vatError.code = extractErrorCode(error, 'VAT_EXPORT_ERROR')
    vatError.details = error
    throw vatError
  }

  // Return the CSV string - RPC returns TEXT
  return String(data ?? '')
}

/**
 * Check if VAT functionality is enabled for an entity
 * Helper function to check feature flag in operating_modes
 */
export async function checkVATEnabled(
  userClient: SupabaseClient<Database>,
  entityId: number
): Promise<boolean> {
  try {
    const { data: operatingMode, error } = await userClient
      .from('operating_modes')
      .select('config')
      .eq('entity_id', entityId)
      .single()

    if (error || !operatingMode) {
      // If no operating mode found, default to disabled for safety
      return false
    }

    const config = operatingMode.config as Record<string, unknown>
    return config?.VATEnabled === true
  } catch {
    // On any error, default to disabled for safety
    return false
  }
}

/**
 * Create VAT feature flag error
 * Helper to create consistent error when VAT is disabled
 */
export function createVATDisabledError(): VATError {
  const error: VATError = new Error(
    'VAT functionality is not enabled for this entity'
  )
  error.code = 'VAT_DISABLED'
  return error
}
type AnyRpcClient = {
  rpc: (
    fn: string,
    args?: Record<string, unknown>
  ) => Promise<{ data: unknown; error: unknown }>
}
