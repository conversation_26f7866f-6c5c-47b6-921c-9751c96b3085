import { PostJournal, JournalLine } from '@ledgerly/types'

// Safe property access utilities for JournalLine fields
function safeCurrencyAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): number {
  if (!line || typeof line !== 'object' || !(field in line)) return 0
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' ? value : 0
}

function hasPositiveAmount(
  line: unknown,
  field: 'debit_amount' | 'credit_amount'
): boolean {
  if (!line || typeof line !== 'object' || !(field in line)) return false
  // eslint-disable-next-line security/detect-object-injection
  const value = (line as Record<string, unknown>)[field]
  return typeof value === 'number' && value > 0
}

// Type guards and safe access utilities
function isValidJournalLine(line: unknown): line is JournalLine {
  return (
    typeof line === 'object' &&
    line !== null &&
    'journal_id' in line &&
    'account_id' in line
  )
}

export interface JournalEntry {
  entityId: number
  description: string
  transactionDate: string
  reference?: string
  lines: JournalLine[]
}

export class PostingService {
  static validateBalance(lines: JournalLine[]): boolean {
    if (!Array.isArray(lines)) {
      return false
    }

    const totalDebits = lines
      .filter(
        line =>
          isValidJournalLine(line) && hasPositiveAmount(line, 'debit_amount')
      )
      .reduce((sum, line) => sum + safeCurrencyAmount(line, 'debit_amount'), 0)

    const totalCredits = lines
      .filter(
        line =>
          isValidJournalLine(line) && hasPositiveAmount(line, 'credit_amount')
      )
      .reduce((sum, line) => sum + safeCurrencyAmount(line, 'credit_amount'), 0)

    return Math.abs(totalDebits - totalCredits) < 0.01
  }

  static createSimpleJournal(
    entityId: number,
    description: string,
    transactionDate: string,
    debitAccountId: number,
    creditAccountId: number,
    amount: number,
    reference?: string
  ): PostJournal {
    const lines: JournalLine[] = [
      {
        journal_id: 0, // Will be set by database
        account_id: debitAccountId,
        description: description,
        debit_amount: amount,
      },
      {
        journal_id: 0, // Will be set by database
        account_id: creditAccountId,
        description: description,
        credit_amount: amount,
      },
    ]

    return {
      entity_id: entityId,
      journal_type: 'general',
      description,
      transaction_date: transactionDate,
      reference,
      lines,
    }
  }

  static createCompoundJournal(
    entityId: number,
    description: string,
    transactionDate: string,
    lines: JournalLine[],
    reference?: string
  ): PostJournal {
    if (lines.length < 2) {
      throw new Error('Journal must have at least 2 lines')
    }

    if (!this.validateBalance(lines)) {
      throw new Error('Journal lines must be balanced')
    }

    return {
      entity_id: entityId,
      journal_type: 'general',
      description,
      transaction_date: transactionDate,
      reference,
      lines,
    }
  }

  static createSaleInvoiceJournal(
    entityId: number,
    invoiceNumber: string,
    customerName: string,
    transactionDate: string,
    receivableAccountId: number,
    salesAccountId: number,
    vatAccountId: number,
    netAmount: number,
    vatAmount: number
  ): PostJournal {
    const totalAmount = netAmount + vatAmount

    const lines: JournalLine[] = [
      {
        journal_id: 0,
        account_id: receivableAccountId,
        description: `Sale to ${customerName} - Invoice ${invoiceNumber}`,
        debit_amount: totalAmount,
      },
      {
        journal_id: 0,
        account_id: salesAccountId,
        description: `Sale to ${customerName} - Invoice ${invoiceNumber}`,
        credit_amount: netAmount,
      },
    ]

    if (vatAmount > 0) {
      lines.push({
        journal_id: 0,
        account_id: vatAccountId,
        description: `VAT on sale to ${customerName} - Invoice ${invoiceNumber}`,
        credit_amount: vatAmount,
      })
    }

    return {
      entity_id: entityId,
      journal_type: 'sales',
      reference: invoiceNumber,
      description: `Sale Invoice ${invoiceNumber} - ${customerName}`,
      transaction_date: transactionDate,
      lines,
    }
  }

  static createPurchaseInvoiceJournal(
    entityId: number,
    invoiceNumber: string,
    supplierName: string,
    transactionDate: string,
    payableAccountId: number,
    expenseAccountId: number,
    vatAccountId: number,
    netAmount: number,
    vatAmount: number
  ): PostJournal {
    const totalAmount = netAmount + vatAmount

    const lines: JournalLine[] = [
      {
        journal_id: 0,
        account_id: expenseAccountId,
        description: `Purchase from ${supplierName} - Invoice ${invoiceNumber}`,
        debit_amount: netAmount,
      },
      {
        journal_id: 0,
        account_id: payableAccountId,
        description: `Purchase from ${supplierName} - Invoice ${invoiceNumber}`,
        credit_amount: totalAmount,
      },
    ]

    if (vatAmount > 0) {
      lines.push({
        journal_id: 0,
        account_id: vatAccountId,
        description: `VAT on purchase from ${supplierName} - Invoice ${invoiceNumber}`,
        debit_amount: vatAmount,
      })
    }

    return {
      entity_id: entityId,
      journal_type: 'purchases',
      reference: invoiceNumber,
      description: `Purchase Invoice ${invoiceNumber} - ${supplierName}`,
      transaction_date: transactionDate,
      lines,
    }
  }

  static createPaymentJournal(
    entityId: number,
    paymentReference: string,
    transactionDate: string,
    bankAccountId: number,
    payableAccountId: number,
    amount: number,
    supplierName: string
  ): PostJournal {
    const lines: JournalLine[] = [
      {
        journal_id: 0,
        account_id: payableAccountId,
        description: `Payment to ${supplierName}`,
        debit_amount: amount,
      },
      {
        journal_id: 0,
        account_id: bankAccountId,
        description: `Payment to ${supplierName}`,
        credit_amount: amount,
      },
    ]

    return {
      entity_id: entityId,
      journal_type: 'payments',
      reference: paymentReference,
      description: `Payment to ${supplierName}`,
      transaction_date: transactionDate,
      lines,
    }
  }
}
