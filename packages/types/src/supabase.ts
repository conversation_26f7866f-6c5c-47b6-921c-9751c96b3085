// Generated from: Staging Supabase ($(date))

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      accounts: {
        Row: {
          account_type: string
          code: string
          created_at: string
          entity_id: number
          id: number
          is_active: boolean | null
          name: string
          normal_balance: string
          parent_id: number | null
          updated_at: string
        }
        Insert: {
          account_type: string
          code: string
          created_at?: string
          entity_id: number
          id?: number
          is_active?: boolean | null
          name: string
          normal_balance: string
          parent_id?: number | null
          updated_at?: string
        }
        Update: {
          account_type?: string
          code?: string
          created_at?: string
          entity_id?: number
          id?: number
          is_active?: boolean | null
          name?: string
          normal_balance?: string
          parent_id?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_events: {
        Row: {
          created_at: string
          entity_id: number | null
          id: number
          new_values: Json | null
          old_values: Json | null
          operation: string
          record_id: number
          table_name: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          entity_id?: number | null
          id?: number
          new_values?: Json | null
          old_values?: Json | null
          operation: string
          record_id: number
          table_name: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          entity_id?: number | null
          id?: number
          new_values?: Json | null
          old_values?: Json | null
          operation?: string
          record_id?: number
          table_name?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_events_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      bank_accounts: {
        Row: {
          account_id: number
          account_number: string
          bank_name: string
          created_at: string
          entity_id: number
          iban: string | null
          id: number
          is_active: boolean | null
          swift: string | null
        }
        Insert: {
          account_id: number
          account_number: string
          bank_name: string
          created_at?: string
          entity_id: number
          iban?: string | null
          id?: number
          is_active?: boolean | null
          swift?: string | null
        }
        Update: {
          account_id?: number
          account_number?: string
          bank_name?: string
          created_at?: string
          entity_id?: number
          iban?: string | null
          id?: number
          is_active?: boolean | null
          swift?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bank_accounts_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_accounts_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      bank_transactions: {
        Row: {
          amount: number
          bank_account_id: number
          batch_id: string | null
          counterparty_account: string | null
          counterparty_name: string | null
          created_at: string
          currency: string | null
          dedupe_hash: string | null
          description: string
          id: number
          is_reconciled: boolean | null
          journal_line_id: number | null
          raw_json: Json | null
          reference: string | null
          status: string | null
          structured_ref: string | null
          transaction_date: string
          transaction_id: string | null
          value_date: string
        }
        Insert: {
          amount: number
          bank_account_id: number
          batch_id?: string | null
          counterparty_account?: string | null
          counterparty_name?: string | null
          created_at?: string
          currency?: string | null
          dedupe_hash?: string | null
          description: string
          id?: number
          is_reconciled?: boolean | null
          journal_line_id?: number | null
          raw_json?: Json | null
          reference?: string | null
          status?: string | null
          structured_ref?: string | null
          transaction_date: string
          transaction_id?: string | null
          value_date: string
        }
        Update: {
          amount?: number
          bank_account_id?: number
          batch_id?: string | null
          counterparty_account?: string | null
          counterparty_name?: string | null
          created_at?: string
          currency?: string | null
          dedupe_hash?: string | null
          description?: string
          id?: number
          is_reconciled?: boolean | null
          journal_line_id?: number | null
          raw_json?: Json | null
          reference?: string | null
          status?: string | null
          structured_ref?: string | null
          transaction_date?: string
          transaction_id?: string | null
          value_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "bank_transactions_bank_account_id_fkey"
            columns: ["bank_account_id"]
            isOneToOne: false
            referencedRelation: "bank_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_transactions_journal_line_id_fkey"
            columns: ["journal_line_id"]
            isOneToOne: false
            referencedRelation: "journal_lines"
            referencedColumns: ["id"]
          },
        ]
      }
      bank_tx_links: {
        Row: {
          amount_applied: number
          bank_transaction_id: number
          created_at: string | null
          created_by: string | null
          entity_id: number
          id: number
          invoice_id: number | null
          journal_id: number | null
          kind: string
        }
        Insert: {
          amount_applied: number
          bank_transaction_id: number
          created_at?: string | null
          created_by?: string | null
          entity_id: number
          id?: number
          invoice_id?: number | null
          journal_id?: number | null
          kind: string
        }
        Update: {
          amount_applied?: number
          bank_transaction_id?: number
          created_at?: string | null
          created_by?: string | null
          entity_id?: number
          id?: number
          invoice_id?: number | null
          journal_id?: number | null
          kind?: string
        }
        Relationships: [
          {
            foreignKeyName: "bank_tx_links_bank_transaction_id_fkey"
            columns: ["bank_transaction_id"]
            isOneToOne: false
            referencedRelation: "bank_transactions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bank_tx_links_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          created_at: string
          document_type: string
          entity_id: number
          file_size: number
          file_url: string
          filename: string
          id: number
          invoice_id: number | null
          journal_id: number | null
          metadata: Json | null
          mime_type: string
        }
        Insert: {
          created_at?: string
          document_type: string
          entity_id: number
          file_size: number
          file_url: string
          filename: string
          id?: number
          invoice_id?: number | null
          journal_id?: number | null
          metadata?: Json | null
          mime_type: string
        }
        Update: {
          created_at?: string
          document_type?: string
          entity_id?: number
          file_size?: number
          file_url?: string
          filename?: string
          id?: number
          invoice_id?: number | null
          journal_id?: number | null
          metadata?: Json | null
          mime_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_journal_id_fkey"
            columns: ["journal_id"]
            isOneToOne: false
            referencedRelation: "journals"
            referencedColumns: ["id"]
          },
        ]
      }
      domain_events: {
        Row: {
          aggregate_id: number
          aggregate_type: string
          created_at: string
          entity_id: number | null
          event_data: Json
          event_type: string
          id: number
          processed_at: string | null
        }
        Insert: {
          aggregate_id: number
          aggregate_type: string
          created_at?: string
          entity_id?: number | null
          event_data: Json
          event_type: string
          id?: number
          processed_at?: string | null
        }
        Update: {
          aggregate_id?: number
          aggregate_type?: string
          created_at?: string
          entity_id?: number | null
          event_data?: Json
          event_type?: string
          id?: number
          processed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "domain_events_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      entities: {
        Row: {
          created_at: string
          currency: string
          fiscal_year_start: string
          id: number
          name: string
          tenant_id: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          currency?: string
          fiscal_year_start?: string
          id?: number
          name: string
          tenant_id: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          currency?: string
          fiscal_year_start?: string
          id?: number
          name?: string
          tenant_id?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "entities_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      entity_memberships: {
        Row: {
          created_at: string
          entity_id: number
          id: number
          role: string
          user_id: string
        }
        Insert: {
          created_at?: string
          entity_id: number
          id?: number
          role: string
          user_id: string
        }
        Update: {
          created_at?: string
          entity_id?: number
          id?: number
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "entity_memberships_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      feature_flags: {
        Row: {
          config: Json | null
          created_at: string
          enabled: boolean
          entity_id: number
          flag: string
          updated_at: string
        }
        Insert: {
          config?: Json | null
          created_at?: string
          enabled?: boolean
          entity_id: number
          flag: string
          updated_at?: string
        }
        Update: {
          config?: Json | null
          created_at?: string
          enabled?: boolean
          entity_id?: number
          flag?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "feature_flags_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      inbox_documents: {
        Row: {
          confidence: number | null
          created_at: string
          entity_id: number
          error_msg: string | null
          export_ref: string | null
          extraction: Json | null
          file_hash: string
          id: number
          mime_type: string
          path: string
          posted_journal_id: number | null
          source: string
          status: string
          suggestion: Json | null
          updated_at: string
        }
        Insert: {
          confidence?: number | null
          created_at?: string
          entity_id: number
          error_msg?: string | null
          export_ref?: string | null
          extraction?: Json | null
          file_hash: string
          id?: number
          mime_type: string
          path: string
          posted_journal_id?: number | null
          source: string
          status: string
          suggestion?: Json | null
          updated_at?: string
        }
        Update: {
          confidence?: number | null
          created_at?: string
          entity_id?: number
          error_msg?: string | null
          export_ref?: string | null
          extraction?: Json | null
          file_hash?: string
          id?: number
          mime_type?: string
          path?: string
          posted_journal_id?: number | null
          source?: string
          status?: string
          suggestion?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inbox_documents_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          counterparty_name: string
          counterparty_vat: string | null
          created_at: string
          due_date: string | null
          entity_id: number
          id: number
          invoice_date: string
          journal_id: number | null
          kind: string
          number: string
          status: string
          total_amount: number
          updated_at: string
          vat_amount: number | null
        }
        Insert: {
          counterparty_name: string
          counterparty_vat?: string | null
          created_at?: string
          due_date?: string | null
          entity_id: number
          id?: number
          invoice_date: string
          journal_id?: number | null
          kind: string
          number: string
          status?: string
          total_amount: number
          updated_at?: string
          vat_amount?: number | null
        }
        Update: {
          counterparty_name?: string
          counterparty_vat?: string | null
          created_at?: string
          due_date?: string | null
          entity_id?: number
          id?: number
          invoice_date?: string
          journal_id?: number | null
          kind?: string
          number?: string
          status?: string
          total_amount?: number
          updated_at?: string
          vat_amount?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_journal_id_fkey"
            columns: ["journal_id"]
            isOneToOne: false
            referencedRelation: "journals"
            referencedColumns: ["id"]
          },
        ]
      }
      journal_lines: {
        Row: {
          account_id: number
          created_at: string
          credit_amount: number | null
          debit_amount: number | null
          description: string | null
          id: number
          journal_id: number
          vat_code_id: number | null
        }
        Insert: {
          account_id: number
          created_at?: string
          credit_amount?: number | null
          debit_amount?: number | null
          description?: string | null
          id?: number
          journal_id: number
          vat_code_id?: number | null
        }
        Update: {
          account_id?: number
          created_at?: string
          credit_amount?: number | null
          debit_amount?: number | null
          description?: string | null
          id?: number
          journal_id?: number
          vat_code_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "journal_lines_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journal_lines_journal_id_fkey"
            columns: ["journal_id"]
            isOneToOne: false
            referencedRelation: "journals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journal_lines_vat_code_id_fkey"
            columns: ["vat_code_id"]
            isOneToOne: false
            referencedRelation: "vat_codes"
            referencedColumns: ["id"]
          },
        ]
      }
      journals: {
        Row: {
          created_at: string
          created_by: string | null
          description: string
          entity_id: number
          id: number
          is_balanced: boolean | null
          journal_type: string
          reference: string | null
          transaction_date: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description: string
          entity_id: number
          id?: number
          is_balanced?: boolean | null
          journal_type?: string
          reference?: string | null
          transaction_date: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string
          entity_id?: number
          id?: number
          is_balanced?: boolean | null
          journal_type?: string
          reference?: string | null
          transaction_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "journals_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      operating_modes: {
        Row: {
          config: Json | null
          created_at: string
          entity_id: number
          id: number
          mode: string
        }
        Insert: {
          config?: Json | null
          created_at?: string
          entity_id: number
          id?: number
          mode: string
        }
        Update: {
          config?: Json | null
          created_at?: string
          entity_id?: number
          id?: number
          mode?: string
        }
        Relationships: [
          {
            foreignKeyName: "operating_modes_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: true
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_invites: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          inviter_user_id: string
          role: string
          scope: string
          scope_id: string
          token: string
        }
        Insert: {
          created_at?: string
          email: string
          expires_at?: string
          inviter_user_id: string
          role: string
          scope: string
          scope_id: string
          token?: string
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          inviter_user_id?: string
          role?: string
          scope?: string
          scope_id?: string
          token?: string
        }
        Relationships: []
      }
      reconciliations: {
        Row: {
          bank_account_id: number
          book_balance: number
          created_at: string
          created_by: string | null
          difference: number
          entity_id: number
          id: number
          reconciliation_date: string
          statement_balance: number
          status: string
        }
        Insert: {
          bank_account_id: number
          book_balance: number
          created_at?: string
          created_by?: string | null
          difference: number
          entity_id: number
          id?: number
          reconciliation_date: string
          statement_balance: number
          status?: string
        }
        Update: {
          bank_account_id?: number
          book_balance?: number
          created_at?: string
          created_by?: string | null
          difference?: number
          entity_id?: number
          id?: number
          reconciliation_date?: string
          statement_balance?: number
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "reconciliations_bank_account_id_fkey"
            columns: ["bank_account_id"]
            isOneToOne: false
            referencedRelation: "bank_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reconciliations_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      security_events: {
        Row: {
          action: string | null
          created_at: string
          details: Json | null
          event_type: string
          id: number
          ip_address: string | null
          resource: string | null
          session_id: string | null
          severity: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action?: string | null
          created_at?: string
          details?: Json | null
          event_type: string
          id?: number
          ip_address?: string | null
          resource?: string | null
          session_id?: string | null
          severity: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string | null
          created_at?: string
          details?: Json | null
          event_type?: string
          id?: number
          ip_address?: string | null
          resource?: string | null
          session_id?: string | null
          severity?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      supplier_templates: {
        Row: {
          created_at: string
          default_account_id: number | null
          default_vat_code_id: number | null
          entity_id: number
          id: number
          last_used_at: string | null
          supplier_name: string | null
          supplier_vat: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          default_account_id?: number | null
          default_vat_code_id?: number | null
          entity_id: number
          id?: number
          last_used_at?: string | null
          supplier_name?: string | null
          supplier_vat?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          default_account_id?: number | null
          default_vat_code_id?: number | null
          entity_id?: number
          id?: number
          last_used_at?: string | null
          supplier_name?: string | null
          supplier_vat?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "supplier_templates_default_account_id_fkey"
            columns: ["default_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_templates_default_vat_code_id_fkey"
            columns: ["default_vat_code_id"]
            isOneToOne: false
            referencedRelation: "vat_codes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplier_templates_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_memberships: {
        Row: {
          created_at: string
          id: number
          role: string
          tenant_id: number
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          role: string
          tenant_id: number
          user_id: string
        }
        Update: {
          created_at?: string
          id?: number
          role?: string
          tenant_id?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tenant_memberships_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenants: {
        Row: {
          created_at: string
          id: number
          kind: string | null
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: number
          kind?: string | null
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: number
          kind?: string | null
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      vat_codes: {
        Row: {
          code: string
          created_at: string
          entity_id: number | null
          id: number
          is_active: boolean | null
          is_purchase: boolean | null
          is_sale: boolean | null
          name: string
          rate: number
        }
        Insert: {
          code: string
          created_at?: string
          entity_id?: number | null
          id?: number
          is_active?: boolean | null
          is_purchase?: boolean | null
          is_sale?: boolean | null
          name: string
          rate: number
        }
        Update: {
          code?: string
          created_at?: string
          entity_id?: number | null
          id?: number
          is_active?: boolean | null
          is_purchase?: boolean | null
          is_sale?: boolean | null
          name?: string
          rate?: number
        }
        Relationships: [
          {
            foreignKeyName: "vat_codes_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      vat_periods: {
        Row: {
          created_at: string
          entity_id: number
          filed_at: string | null
          id: number
          period_end: string
          period_start: string
          status: string
        }
        Insert: {
          created_at?: string
          entity_id: number
          filed_at?: string | null
          id?: number
          period_end: string
          period_start: string
          status?: string
        }
        Update: {
          created_at?: string
          entity_id?: number
          filed_at?: string | null
          id?: number
          period_end?: string
          period_start?: string
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "vat_periods_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      v_user_entities: {
        Row: {
          entity_id: number | null
          entity_name: string | null
          role: string | null
          tenant_id: number | null
          tenant_name: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "entities_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "entity_memberships_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
        ]
      }
      v_user_tenants: {
        Row: {
          role: string | null
          tenant_id: number | null
          tenant_kind: string | null
          tenant_name: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_memberships_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      citext: {
        Args: { "": boolean } | { "": string } | { "": unknown }
        Returns: string
      }
      citext_hash: {
        Args: { "": string }
        Returns: number
      }
      citextin: {
        Args: { "": unknown }
        Returns: string
      }
      citextout: {
        Args: { "": string }
        Returns: unknown
      }
      citextrecv: {
        Args: { "": unknown }
        Returns: string
      }
      citextsend: {
        Args: { "": string }
        Returns: string
      }
      cleanup_old_security_events: {
        Args: { retention_days?: number }
        Returns: number
      }
      detect_suspicious_ips: {
        Args: {
          failed_login_threshold?: number
          rate_limit_threshold?: number
          time_window_minutes?: number
        }
        Returns: {
          failed_logins: number
          ip_address: unknown
          rate_limit_violations: number
          risk_score: number
          total_events: number
        }[]
      }
      get_security_event_stats: {
        Args: { event_types?: string[]; time_window_hours?: number }
        Returns: {
          event_count: number
          event_type: string
          severity_breakdown: Json
          unique_ips: number
          unique_users: number
        }[]
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      l2_norm: {
        Args: { "": unknown }  
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown }  
        Returns: string
      }
      rpc_accept_invite: {
        Args: { p_token: string }
        Returns: undefined
      }
      rpc_create_entity: {
        Args: { p_currency?: string; p_name: string; p_tenant_id: number }
        Returns: number
      }
      rpc_create_invoice_with_journal: {
        Args: {
          p_accounting_data: Json
          p_entity: number
          p_invoice_data: Json
        }
        Returns: number
      }
      rpc_create_tenant: {
        Args: { p_kind?: string; p_name: string }
        Returns: number
      }
      rpc_get_entity_summary: {
        Args: { p_entity: number }
        Returns: Json
      }
      rpc_grant_entity_role: {
        Args: { p_entity: number; p_role: string; p_user: string }
        Returns: undefined
      }
      rpc_grant_tenant_role: {
        Args: { p_role: string; p_target_user: string; p_tenant_id: number }
        Returns: undefined
      }
      rpc_import_bank_transactions: {
        Args: {
          p_bank_account_id: number
          p_entity: number
          p_transactions: Json
        }
        Returns: Json
      }
      rpc_invite: {
        Args: {
          p_email: string
          p_role: string
          p_scope: string
          p_scope_id: string
        }
        Returns: string
      }
      rpc_post_journal: {
        Args: {
          p_date: string
          p_description: string
          p_entity: number
          p_lines: Json
          p_reference?: string
          p_type: string
        }
        Returns: number
      }
      rpc_reconcile_bank_transaction: {
        Args: { p_bank_transaction_id: number; p_journal_line_id: number }
        Returns: undefined
      }
      rpc_switch_mode: {
        Args: { p_config?: Json; p_entity: number; p_mode: string }
        Returns: undefined
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const
