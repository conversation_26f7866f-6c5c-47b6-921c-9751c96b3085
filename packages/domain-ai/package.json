{"name": "@ledgerly/domain-ai", "version": "1.0.0", "description": "AI-powered suggestion blending utilities for Ledgerly", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit", "test": "vitest"}, "keywords": ["ai", "suggestions", "machine-learning", "embeddings", "blending"], "author": "Ledgerly Team", "license": "MIT", "dependencies": {}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "vitest": "^3.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0"}, "files": ["dist", "README.md"]}